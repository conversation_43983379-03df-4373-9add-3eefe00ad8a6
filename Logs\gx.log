7/14 02:48:11.266  CGxDevice::DeviceAdapterID(): RET: 1, VID: 10de, DID: 1c82, DVER: 0.0
7/14 02:48:11.386  CGxDevice::DeviceAdapterInfer(): RET: 1, DID: 3
7/14 02:48:11.386  ConsoleDetectDetectHardware():
7/14 02:48:11.386  	cpuIdx: 0
7/14 02:48:11.386  	videoID: 693
7/14 02:48:11.386  	soundIdx: 0
7/14 02:48:11.386  	memIdx: 0
7/14 02:48:11.426  ValidateFormatMonitor(): unable to find monitor refresh
7/14 02:48:11.426  ValidateFormatMonitor(): invalid refresh rate 60, set to 60
7/14 02:48:11.426  ConsoleDeviceInitialize(): hwDetect = 0, hwChanged = 0
7/14 02:48:11.510  CGxDeviceD3d::DeviceSetFormat():
7/14 02:48:11.510  	Format: 1920 x 1080 Window, Ds24X, multisample 8
7/14 02:48:11.626  Caps:
7/14 02:48:11.626  	numTmus: 8
7/14 02:48:11.626  	generateMipMaps: 1
7/14 02:48:11.626  	texFilterAnisotropic: 1, 16
7/14 02:48:11.626  	rttFormat: 1, 1
7/14 02:48:11.626  	pixelShaderTarget: ps_3_0
7/14 02:48:11.626  	vertexShaderTarget: vs_3_0
7/14 02:48:11.626  	vertexShaderConstants: 256
7/14 02:48:11.626  	numStreams: 16
7/14 02:48:11.626  	stereoAvailable: 0
7/14 02:48:11.626  	NVAPI: 1
7/14 02:48:11.626  	stereoHandle: 0
