7/13 21:43:11.954  CGxDevice::DeviceAdapterID(): RET: 1, VID: 10de, DID: 1c82, DVER: 0.0
7/13 21:43:12.116  CGxDevice::DeviceAdapterInfer(): RET: 1, DID: 3
7/13 21:43:12.116  ConsoleDetectDetectHardware():
7/13 21:43:12.116  	cpuIdx: 0
7/13 21:43:12.116  	videoID: 693
7/13 21:43:12.116  	soundIdx: 0
7/13 21:43:12.116  	memIdx: 0
7/13 21:43:12.175  ValidateFormatMonitor(): unable to find monitor refresh
7/13 21:43:12.175  ValidateFormatMonitor(): invalid refresh rate 60, set to 60
7/13 21:43:12.175  ConsoleDeviceInitialize(): hwDetect = 0, hwChanged = 0
7/13 21:43:12.276  CGxDeviceD3d::DeviceSetFormat():
7/13 21:43:12.276  	Format: 1920 x 1080 Window, Ds24X, multisample 8
7/13 21:43:12.414  Caps:
7/13 21:43:12.414  	numTmus: 8
7/13 21:43:12.414  	generateMipMaps: 1
7/13 21:43:12.414  	texFilterAnisotropic: 1, 16
7/13 21:43:12.414  	rttFormat: 1, 1
7/13 21:43:12.414  	pixelShaderTarget: ps_3_0
7/13 21:43:12.414  	vertexShaderTarget: vs_3_0
7/13 21:43:12.414  	vertexShaderConstants: 256
7/13 21:43:12.414  	numStreams: 16
7/13 21:43:12.414  	stereoAvailable: 0
7/13 21:43:12.414  	NVAPI: 1
7/13 21:43:12.414  	stereoHandle: 0
