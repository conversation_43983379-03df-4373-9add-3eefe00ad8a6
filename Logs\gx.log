7/13 23:46:56.657  CGxDevice::DeviceAdapterID(): RET: 1, VID: 10de, DID: 1c82, DVER: 0.0
7/13 23:46:56.792  CGxDevice::DeviceAdapterInfer(): RET: 1, DID: 3
7/13 23:46:56.792  ConsoleDetectDetectHardware():
7/13 23:46:56.792  	cpuIdx: 0
7/13 23:46:56.792  	videoID: 693
7/13 23:46:56.792  	soundIdx: 0
7/13 23:46:56.792  	memIdx: 0
7/13 23:46:56.842  ValidateFormatMonitor(): unable to find monitor refresh
7/13 23:46:56.842  ValidateFormatMonitor(): invalid refresh rate 60, set to 60
7/13 23:46:56.842  ConsoleDeviceInitialize(): hwDetect = 0, hwChanged = 0
7/13 23:46:56.952  CGxDeviceD3d::DeviceSetFormat():
7/13 23:46:56.952  	Format: 1920 x 1080 Window, Ds24X, multisample 8
7/13 23:46:57.098  Caps:
7/13 23:46:57.098  	numTmus: 8
7/13 23:46:57.098  	generateMipMaps: 1
7/13 23:46:57.098  	texFilterAnisotropic: 1, 16
7/13 23:46:57.098  	rttFormat: 1, 1
7/13 23:46:57.098  	pixelShaderTarget: ps_3_0
7/13 23:46:57.098  	vertexShaderTarget: vs_3_0
7/13 23:46:57.098  	vertexShaderConstants: 256
7/13 23:46:57.098  	numStreams: 16
7/13 23:46:57.098  	stereoAvailable: 0
7/13 23:46:57.098  	NVAPI: 1
7/13 23:46:57.098  	stereoHandle: 0
