7/13 23:42:08.407  CGxDevice::DeviceAdapterID(): RET: 1, VID: 10de, DID: 1c82, DVER: 0.0
7/13 23:42:08.556  CGxDevice::DeviceAdapterInfer(): RET: 1, DID: 3
7/13 23:42:08.556  ConsoleDetectDetectHardware():
7/13 23:42:08.556  	cpuIdx: 0
7/13 23:42:08.556  	videoID: 693
7/13 23:42:08.556  	soundIdx: 0
7/13 23:42:08.556  	memIdx: 0
7/13 23:42:08.631  ValidateFormatMonitor(): unable to find monitor refresh
7/13 23:42:08.631  ValidateFormatMonitor(): invalid refresh rate 60, set to 60
7/13 23:42:08.631  ConsoleDeviceInitialize(): hwDetect = 0, hwChanged = 0
7/13 23:42:08.730  CGxDeviceD3d::DeviceSetFormat():
7/13 23:42:08.730  	Format: 1920 x 1080 Window, Ds24X, multisample 8
7/13 23:42:08.854  Caps:
7/13 23:42:08.854  	numTmus: 8
7/13 23:42:08.854  	generateMipMaps: 1
7/13 23:42:08.854  	texFilterAnisotropic: 1, 16
7/13 23:42:08.854  	rttFormat: 1, 1
7/13 23:42:08.854  	pixelShaderTarget: ps_3_0
7/13 23:42:08.854  	vertexShaderTarget: vs_3_0
7/13 23:42:08.854  	vertexShaderConstants: 256
7/13 23:42:08.854  	numStreams: 16
7/13 23:42:08.854  	stereoAvailable: 0
7/13 23:42:08.854  	NVAPI: 1
7/13 23:42:08.854  	stereoHandle: 0
