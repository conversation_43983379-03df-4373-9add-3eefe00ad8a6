7/14 01:12:23.891  CGxDevice::DeviceAdapterID(): RET: 1, VID: 10de, DID: 1c82, DVER: 0.0
7/14 01:12:24.015  CGxDevice::DeviceAdapterInfer(): RET: 1, DID: 3
7/14 01:12:24.015  ConsoleDetectDetectHardware():
7/14 01:12:24.015  	cpuIdx: 0
7/14 01:12:24.015  	videoID: 693
7/14 01:12:24.015  	soundIdx: 0
7/14 01:12:24.015  	memIdx: 0
7/14 01:12:24.059  ValidateFormatMonitor(): unable to find monitor refresh
7/14 01:12:24.059  ValidateFormatMonitor(): invalid refresh rate 60, set to 60
7/14 01:12:24.059  ConsoleDeviceInitialize(): hwDetect = 0, hwChanged = 0
7/14 01:12:24.143  CGxDeviceD3d::DeviceSetFormat():
7/14 01:12:24.143  	Format: 1920 x 1080 Window, Ds24X, multisample 8
7/14 01:12:24.253  Caps:
7/14 01:12:24.253  	numTmus: 8
7/14 01:12:24.253  	generateMipMaps: 1
7/14 01:12:24.253  	texFilterAnisotropic: 1, 16
7/14 01:12:24.253  	rttFormat: 1, 1
7/14 01:12:24.253  	pixelShaderTarget: ps_3_0
7/14 01:12:24.253  	vertexShaderTarget: vs_3_0
7/14 01:12:24.253  	vertexShaderConstants: 256
7/14 01:12:24.253  	numStreams: 16
7/14 01:12:24.253  	stereoAvailable: 0
7/14 01:12:24.253  	NVAPI: 1
7/14 01:12:24.253  	stereoHandle: 0
