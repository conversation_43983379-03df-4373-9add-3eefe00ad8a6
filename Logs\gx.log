7/14 00:43:38.143  CGxDevice::DeviceAdapterID(): RET: 1, VID: 10de, DID: 1c82, DVER: 0.0
7/14 00:43:38.369  CGxDevice::DeviceAdapterInfer(): RET: 1, DID: 3
7/14 00:43:38.369  ConsoleDetectDetectHardware():
7/14 00:43:38.369  	cpuIdx: 0
7/14 00:43:38.369  	videoID: 693
7/14 00:43:38.369  	soundIdx: 0
7/14 00:43:38.369  	memIdx: 0
7/14 00:43:38.460  ValidateFormatMonitor(): unable to find monitor refresh
7/14 00:43:38.460  ValidateFormatMonitor(): invalid refresh rate 60, set to 60
7/14 00:43:38.460  ConsoleDeviceInitialize(): hwDetect = 0, hwChanged = 0
7/14 00:43:38.620  CGxDeviceD3d::DeviceSetFormat():
7/14 00:43:38.620  	Format: 1920 x 1080 Window, Ds24X, multisample 8
7/14 00:43:38.765  Caps:
7/14 00:43:38.765  	numTmus: 8
7/14 00:43:38.765  	generateMipMaps: 1
7/14 00:43:38.765  	texFilterAnisotropic: 1, 16
7/14 00:43:38.765  	rttFormat: 1, 1
7/14 00:43:38.765  	pixelShaderTarget: ps_3_0
7/14 00:43:38.765  	vertexShaderTarget: vs_3_0
7/14 00:43:38.765  	vertexShaderConstants: 256
7/14 00:43:38.765  	numStreams: 16
7/14 00:43:38.765  	stereoAvailable: 0
7/14 00:43:38.765  	NVAPI: 1
7/14 00:43:38.765  	stereoHandle: 0
