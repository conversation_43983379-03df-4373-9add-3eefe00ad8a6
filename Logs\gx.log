7/14 02:57:02.986  CGxDevice::DeviceAdapterID(): RET: 1, VID: 10de, DID: 1c82, DVER: 0.0
7/14 02:57:03.144  CGxDevice::DeviceAdapterInfer(): RET: 1, DID: 3
7/14 02:57:03.144  ConsoleDetectDetectHardware():
7/14 02:57:03.144  	cpuIdx: 0
7/14 02:57:03.144  	videoID: 693
7/14 02:57:03.144  	soundIdx: 0
7/14 02:57:03.144  	memIdx: 0
7/14 02:57:03.213  ValidateFormatMonitor(): unable to find monitor refresh
7/14 02:57:03.213  ValidateFormatMonitor(): invalid refresh rate 60, set to 60
7/14 02:57:03.213  ConsoleDeviceInitialize(): hwDetect = 0, hwChanged = 0
7/14 02:57:03.321  CGxDeviceD3d::DeviceSetFormat():
7/14 02:57:03.321  	Format: 1920 x 1080 Window, Ds24X, multisample 8
7/14 02:57:03.419  Caps:
7/14 02:57:03.419  	numTmus: 8
7/14 02:57:03.419  	generateMipMaps: 1
7/14 02:57:03.419  	texFilterAnisotropic: 1, 16
7/14 02:57:03.419  	rttFormat: 1, 1
7/14 02:57:03.419  	pixelShaderTarget: ps_3_0
7/14 02:57:03.419  	vertexShaderTarget: vs_3_0
7/14 02:57:03.419  	vertexShaderConstants: 256
7/14 02:57:03.419  	numStreams: 16
7/14 02:57:03.419  	stereoAvailable: 0
7/14 02:57:03.419  	NVAPI: 1
7/14 02:57:03.419  	stereoHandle: 0
