7/14 00:15:07.439  CGxDevice::DeviceAdapterID(): RET: 1, VID: 10de, DID: 1c82, DVER: 0.0
7/14 00:15:07.638  CGxDevice::DeviceAdapterInfer(): RET: 1, DID: 3
7/14 00:15:07.638  ConsoleDetectDetectHardware():
7/14 00:15:07.638  	cpuIdx: 0
7/14 00:15:07.638  	videoID: 693
7/14 00:15:07.638  	soundIdx: 0
7/14 00:15:07.638  	memIdx: 0
7/14 00:15:07.723  ValidateFormatMonitor(): unable to find monitor refresh
7/14 00:15:07.723  ValidateFormatMonitor(): invalid refresh rate 60, set to 60
7/14 00:15:07.723  ConsoleDeviceInitialize(): hwDetect = 0, hwChanged = 0
7/14 00:15:07.882  CGxDeviceD3d::DeviceSetFormat():
7/14 00:15:07.882  	Format: 1920 x 1080 Window, Ds24X, multisample 8
7/14 00:15:08.047  Caps:
7/14 00:15:08.047  	numTmus: 8
7/14 00:15:08.047  	generateMipMaps: 1
7/14 00:15:08.047  	texFilterAnisotropic: 1, 16
7/14 00:15:08.047  	rttFormat: 1, 1
7/14 00:15:08.047  	pixelShaderTarget: ps_3_0
7/14 00:15:08.047  	vertexShaderTarget: vs_3_0
7/14 00:15:08.047  	vertexShaderConstants: 256
7/14 00:15:08.047  	numStreams: 16
7/14 00:15:08.047  	stereoAvailable: 0
7/14 00:15:08.047  	NVAPI: 1
7/14 00:15:08.047  	stereoHandle: 0
