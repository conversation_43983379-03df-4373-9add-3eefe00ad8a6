7/14 02:23:30.642  CGxDevice::DeviceAdapterID(): RET: 1, VID: 10de, DID: 1c82, DVER: 0.0
7/14 02:23:30.800  CGxDevice::DeviceAdapterInfer(): RET: 1, DID: 3
7/14 02:23:30.800  ConsoleDetectDetectHardware():
7/14 02:23:30.800  	cpuIdx: 0
7/14 02:23:30.800  	videoID: 693
7/14 02:23:30.800  	soundIdx: 0
7/14 02:23:30.800  	memIdx: 0
7/14 02:23:30.850  ValidateFormatMonitor(): unable to find monitor refresh
7/14 02:23:30.850  ValidateFormatMonitor(): invalid refresh rate 60, set to 60
7/14 02:23:30.850  ConsoleDeviceInitialize(): hwDetect = 0, hwChanged = 0
7/14 02:23:30.974  CGxDeviceD3d::DeviceSetFormat():
7/14 02:23:30.974  	Format: 1920 x 1080 Window, Ds24X, multisample 8
7/14 02:23:31.087  Caps:
7/14 02:23:31.087  	numTmus: 8
7/14 02:23:31.087  	generateMipMaps: 1
7/14 02:23:31.087  	texFilterAnisotropic: 1, 16
7/14 02:23:31.087  	rttFormat: 1, 1
7/14 02:23:31.087  	pixelShaderTarget: ps_3_0
7/14 02:23:31.087  	vertexShaderTarget: vs_3_0
7/14 02:23:31.087  	vertexShaderConstants: 256
7/14 02:23:31.087  	numStreams: 16
7/14 02:23:31.087  	stereoAvailable: 0
7/14 02:23:31.087  	NVAPI: 1
7/14 02:23:31.087  	stereoHandle: 0
