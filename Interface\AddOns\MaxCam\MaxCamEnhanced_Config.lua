MaxCamEnhanced = MaxCamEnhanced or {}
local AceConfig = LibStub("AceConfig-3.0")
local AceConfigDialog = LibStub("AceConfigDialog-3.0")
local AceConfigRegistry = LibStub("AceConfigRegistry-3.0")
local L = MaxCamEnhanced_Locale or {}

local options = nil

local function get(info)
    if not MaxCamEnhanced.db then
        return nil
    end
    return MaxCamEnhanced.db.profile[info[#info]]
end

local function set(info, value)
    if not MaxCamEnhanced.db then
        return
    end
    MaxCamEnhanced.db.profile[info[#info]] = value

    -- Always save settings to database
    if MaxCamEnhanced.db then
        MaxCamEnhanced.db:SetProfile(MaxCamEnhanced.db:GetCurrentProfile())
    end

    -- Apply settings immediately if auto-apply is enabled
    if MaxCamEnhanced.db.profile.autoApply then
        MaxCamEnhanced:ApplySettings()
    end
end

local function createOptions()
    if options then
        return options
    end
    
    options = {
        type = "group",
        name = "MaxCamEnhanced",
        desc = L["Advanced camera distance control with configurable settings"] or "Advanced camera distance control with configurable settings",
        args = {
            header = {
                type = "header",
                name = L["MaxCamEnhanced Settings"] or "MaxCamEnhanced Settings",
                order = 1,
            },
            -- Main toggles at the top for convenience
            enabled = {
                type = "toggle",
                name = L["Enable Camera Distance"] or "Enable Camera Distance",
                desc = L["Enable camera distance extension (can be toggled on/off)"] or "Enable camera distance extension (can be toggled on/off)",
                order = 2,
                get = get,
                set = set,
            },
            autoApply = {
                type = "toggle",
                name = L["Auto Apply Changes"] or "Auto Apply Changes",
                desc = L["Automatically apply settings when you change them"] or "Automatically apply settings when you change them",
                order = 3,
                get = get,
                set = set,
            },
            spacer1 = {
                type = "header",
                name = L["Camera Distance Settings"] or "Camera Distance Settings",
                order = 4,
            },
            cameraDistance = {
                type = "range",
                name = L["Maximum Camera Distance"] or "Maximum Camera Distance",
                desc = L["Controls maximum camera distance from player (default: 15, max: 50)"] or "Controls maximum camera distance from player (default: 15, max: 50)",
                min = 15,
                max = 50,
                step = 1,
                bigStep = 5,
                order = 5,
                get = get,
                set = set,
                disabled = function() return not MaxCamEnhanced.db.profile.enabled end,
            },
            applyOnZoneChange = {
                type = "toggle",
                name = L["Apply on Zone Change"] or "Apply on Zone Change",
                desc = L["Automatically apply camera distance when entering new zones"] or "Automatically apply camera distance when entering new zones",
                order = 6,
                get = get,
                set = set,
                disabled = function() return not MaxCamEnhanced.db.profile.enabled end,
            },
            spacer2 = {
                type = "header",
                name = L["Actions"] or "Actions",
                order = 7,
            },
            apply = {
                type = "execute",
                name = L["Apply Settings Now"] or "Apply Settings Now",
                desc = L["Apply current camera distance settings immediately"] or "Apply current camera distance settings immediately",
                order = 8,
                func = function()
                    MaxCamEnhanced:ApplySettings()
                    if MaxCamEnhanced.db.profile.enabled then
                        print("|cFF00FF00MaxCamEnhanced:|r " .. (L["Camera distance applied:"] or "Camera distance applied:") .. " " .. MaxCamEnhanced.db.profile.cameraDistance)
                    else
                        print("|cFF00FF00MaxCamEnhanced:|r " .. (L["Camera distance disabled"] or "Camera distance disabled"))
                    end
                end,
            },
            reset = {
                type = "execute",
                name = L["Reset to Defaults"] or "Reset to Defaults",
                desc = L["Reset all settings to default values (distance: 20, enabled)"] or "Reset all settings to default values (distance: 20, enabled)",
                order = 9,
                func = function()
                    MaxCamEnhanced:ResetToDefaults()
                    print("|cFF00FF00MaxCamEnhanced:|r " .. (L["Reset to defaults"] or "Reset to defaults"))
                end,
            },
        }
    }

    return options
end

-- Configuration initialization function (called after addon load)
function MaxCamEnhanced.InitializeConfig()
    if MaxCamEnhanced.configInitialized then
        return -- Prevent duplicate registration
    end

    local options = createOptions()
    -- Unregister any existing registration first
    pcall(function() AceConfigDialog:Close("MaxCamEnhanced") end)

    AceConfig:RegisterOptionsTable("MaxCamEnhanced", options)
    AceConfigDialog:AddToBlizOptions("MaxCamEnhanced", "MaxCamEnhanced")

    -- Hook interface close to apply settings
    local originalHide = InterfaceOptionsFrame.Hide
    InterfaceOptionsFrame.Hide = function(self)
        -- Apply settings when interface is closed
        if MaxCamEnhanced and MaxCamEnhanced.ApplySettings then
            MaxCamEnhanced:ApplySettings()
        end
        originalHide(self)
    end

    MaxCamEnhanced.configInitialized = true
end
