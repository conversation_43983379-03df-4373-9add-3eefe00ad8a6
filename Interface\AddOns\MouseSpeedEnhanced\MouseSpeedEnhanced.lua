-- MouseSpeed Enhanced - Advanced mouse and camera sensitivity control using Ace3
-- Version 2.0

-- Create addon using AceAddon-3.0
local MouseSpeedEnhanced = LibStub("AceAddon-3.0"):NewAddon("MouseSpeedEnhanced", "AceEvent-3.0", "AceConsole-3.0")
local L = MouseSpeedEnhanced_Locale or {}

-- Export to global scope
_G["MouseSpeedEnhanced"] = MouseSpeedEnhanced

-- Default settings (like in original code)
local defaults = {
    profile = {
        mouseSpeed = 0.17,
        cameraYawMoveSpeed = 55,
        cameraPitchMoveSpeed = 55,
        autoApply = true,
        enableOnLogin = false  -- Camera disabled by default (like in original)
    }
}

function MouseSpeedEnhanced:OnInitialize()
    -- Initialize database
    self.db = LibStub("AceDB-3.0"):New("MouseSpeedEnhancedDB", defaults, "Default")

    -- Register slash commands
    self:RegisterChatCommand("mousespeed", "SlashCmdHandler")
    self:RegisterChatCommand("ms", "SlashCmdHandler")

    print("|cFF00FF00MouseSpeedEnhanced v2.0 " .. (L["loaded! Type /ms config for settings."] or "loaded! Type /ms config for settings.") .. "|r")
end

function MouseSpeedEnhanced:OnEnable()
    -- Register world enter event (like in original addon)
    self:RegisterEvent("PLAYER_ENTERING_WORLD")

    -- Initialize configuration with small delay
    C_Timer.After(0.1, function()
        if MouseSpeedEnhanced.InitializeConfig then
            MouseSpeedEnhanced.InitializeConfig()
        end
    end)
end

function MouseSpeedEnhanced:OnDisable()
    -- Clean up configuration registration
    self.configInitialized = false
end

-- Event handler (exactly like in original addon)
function MouseSpeedEnhanced:PLAYER_ENTERING_WORLD()
    -- Always apply mouseSpeed (like in original)
    SetCVar("mouseSpeed", self.db.profile.mouseSpeed)

    -- Apply camera settings only if enabled
    if self.db.profile.enableOnLogin then
        SetCVar("cameraYawMoveSpeed", self.db.profile.cameraYawMoveSpeed)
        SetCVar("cameraPitchMoveSpeed", self.db.profile.cameraPitchMoveSpeed)
    end

    -- Unregister event after first application (like in original)
    self:UnregisterEvent("PLAYER_ENTERING_WORLD")
end

-- Apply settings to game
function MouseSpeedEnhanced:ApplySettings()
    SetCVar("mouseSpeed", self.db.profile.mouseSpeed)
    if self.db.profile.enableOnLogin then
        SetCVar("cameraYawMoveSpeed", self.db.profile.cameraYawMoveSpeed)
        SetCVar("cameraPitchMoveSpeed", self.db.profile.cameraPitchMoveSpeed)
    end

    -- Force save settings to database
    if self.db then
        self.db:SetProfile(self.db:GetCurrentProfile())
    end

    -- Remove annoying messages - settings apply silently
end

-- Reset to defaults
function MouseSpeedEnhanced:ResetToDefaults()
    self.db:ResetProfile()
    self:ApplySettings()
    -- Remove annoying messages - reset happens silently
end

-- Slash command handler
function MouseSpeedEnhanced:SlashCmdHandler(input)
    local command = string.lower(input or "")

    if command == "config" or command == "settings" or command == "" then
        -- Open Blizzard interface options to our addon
        InterfaceOptionsFrame_OpenToCategory("MouseSpeedEnhanced")
    elseif command == "apply" then
        self:ApplySettings()
    elseif command == "reset" then
        self:ResetToDefaults()
    elseif command == "help" then
        print("|cFF00FF00MouseSpeedEnhanced " .. (L["Commands:"] or "Commands:") .. "|r")
        print("|cFFFFFF00/ms|r or |cFFFFFF00/mousespeed|r - " .. (L["Open settings"] or "Open settings"))
        print("|cFFFFFF00/ms apply|r - " .. (L["Apply current settings"] or "Apply current settings"))
        print("|cFFFFFF00/ms reset|r - " .. (L["Reset to defaults"] or "Reset to defaults"))
        print("|cFFFFFF00/ms help|r - " .. (L["Show this help"] or "Show this help"))
    else
        print("|cFFFF0000MouseSpeedEnhanced:|r " .. (L["Unknown command. Type '/ms help' for available commands."] or "Unknown command. Type '/ms help' for available commands."))
    end
end

-- Update current values display in config
function MouseSpeedEnhanced:UpdateCurrentValues()
    -- Update configuration interface if it's open
    if LibStub and LibStub("AceConfigRegistry-3.0", true) then
        LibStub("AceConfigRegistry-3.0"):NotifyChange("MouseSpeedEnhanced")
    end
end

