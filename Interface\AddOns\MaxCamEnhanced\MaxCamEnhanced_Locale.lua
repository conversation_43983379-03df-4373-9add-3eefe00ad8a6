-- MaxCamEnhanced Localization
-- Supports English and Russian

local L = {}

-- Default English strings
L["MaxCamEnhanced"] = "MaxCamEnhanced"
L["Advanced camera distance control with configurable settings"] = "Advanced camera distance control with configurable settings"
L["MaxCamEnhanced Settings"] = "MaxCamEnhanced Settings"

-- Main settings
L["Enable Camera Distance"] = "Enable Camera Distance"
L["Enable camera distance extension (can be toggled on/off)"] = "Enable camera distance extension (can be toggled on/off)"
L["Auto Apply Changes"] = "Auto Apply Changes"
L["Automatically apply settings when you change them"] = "Automatically apply settings when you change them"
L["Unify Camera Distance"] = "Unify Camera Distance"
L["Set the same camera distance for all characters (recommended for consistent experience)"] = "Set the same camera distance for all characters (recommended for consistent experience)"
L["Auto-Detect Optimal Distance"] = "Auto-Detect Optimal Distance"
L["Automatically detect optimal camera distance based on character's current camera position"] = "Automatically adjust camera distance based on current location (indoor/outdoor, dungeons, PvP zones)"


-- Sections
L["Camera Distance Settings"] = "Camera Distance Settings"
L["Actions"] = "Actions"

-- Camera settings
L["Camera Distance"] = "Camera Distance"
L["Controls camera distance from player (1-100)"] = "Controls camera distance from player (1-100)"
L["Apply on Zone Change"] = "Apply on Zone Change"
L["Automatically apply camera distance when entering new zones"] = "Automatically apply camera distance when entering new zones"





-- Actions
L["Apply Settings Now"] = "Apply Settings Now"
L["Apply current camera distance settings immediately"] = "Apply current camera distance settings immediately (includes auto-detection if enabled)"
L["Reset to Defaults"] = "Reset to Defaults"
L["Reset all settings to default values (distance: 30, enabled, unified)"] = "Reset all settings to default values (distance: 30, enabled, unified). Unify mode provides consistent camera experience across all characters."



-- Messages
L["loaded! Type /maxcam config for settings."] = "loaded! Type /maxcam config for settings."
L["Commands:"] = "Commands:"
L["Open settings"] = "Open settings"
L["Apply current settings"] = "Apply current settings"
L["Reset to defaults"] = "Reset to defaults"

-- Camera Pitch Settings
L["Camera Pitch Settings"] = "Camera Pitch Settings"
L["Control Camera Pitch"] = "Control Camera Pitch"
L["Enable camera pitch (angle) control - may not work reliably in WoW 3.3.5a"] = "Enable camera pitch (angle) control - may not work reliably in WoW 3.3.5a"
L["Camera Pitch"] = "Camera Pitch"
L["Camera pitch angle in degrees (-30 to 60, default: 20)"] = "Camera pitch angle in degrees (-30 to 60, default: 20)"
L["(controlled by addon)"] = "(controlled by addon)"
L["(not controlled)"] = "(not controlled)"
L["Show this help"] = "Show this help"
L["Unknown command. Type '/maxcam help' for available commands."] = "Unknown command. Type '/maxcam help' for available commands."
L["Camera distance applied:"] = "Camera distance applied:"
L["Camera distance disabled"] = "Camera distance disabled"

-- Tabs
L["General"] = "General"
L["Auto-Detection"] = "Auto-Detection"

-- Auto-Detection Settings
L["Location-Based Distances"] = "Location-Based Distances"
L["Instance Distance"] = "Instance Distance"
L["Camera distance for dungeons and raids (20-30 recommended)"] = "Camera distance for dungeons and raids (20-30 recommended)"
L["Indoor Distance"] = "Indoor Distance"
L["Camera distance for indoor locations like buildings and caves (15-25 recommended)"] = "Camera distance for indoor locations like buildings and caves (15-25 recommended)"
L["Outdoor Distance"] = "Outdoor Distance"
L["Camera distance for outdoor areas and open world (35-50 recommended)"] = "Camera distance for outdoor areas and open world (35-50 recommended)"
L["PvP Distance"] = "PvP Distance"
L["Camera distance for PvP zones for maximum awareness (50 recommended)"] = "Camera distance for PvP zones for maximum awareness (50 recommended)"
L["City Distance"] = "City Distance"
L["Camera distance for major cities (25-30 recommended)"] = "Camera distance for major cities (25-30 recommended)"

L["Zone Overrides"] = "Zone Overrides"
L["Specific distance settings for individual zones (overrides location-based settings)"] = "Specific distance settings for individual zones (overrides location-based settings)"

L["Auto-Detection Settings"] = "Auto-Detection Settings"
L["Configure automatic camera distance detection based on location type and specific zones"] = "Configure automatic camera distance detection based on location type and specific zones"

-- Current Values (restored)
L["Current WTF Values:"] = "Current WTF Values:"
L["Current Camera Distance:"] = "Current Camera Distance:"
L["(actual zoom level)"] = "(saved zoom position)"
L["Camera Saved Distance:"] = "Camera Saved Distance:"
L["(default position)"] = "(default position)"
L["Camera Max Distance:"] = "Camera Max Distance:"
L["(zoom out limit)"] = "(zoom out limit)"
L["Camera Pitch:"] = "Camera Pitch:"
L["(controlled by addon)"] = "(controlled by addon)"
L["(not controlled)"] = "(not controlled)"
L["Camera Distance Setting:"] = "Camera Distance Setting:"
L["(both position and zoom limit)"] = "(both position and zoom limit)"
L["Settings look correct"] = "Settings look correct"
L["(Values are automatically unified on login)"] = "(Values are automatically unified on login)"
L["(Enable unification to standardize values)"] = "(Enable unification to standardize values)"

-- Russian localization
if GetLocale() == "ruRU" then
    L["MaxCamEnhanced"] = "MaxCamEnhanced"
    L["Advanced camera distance control with configurable settings"] = "Продвинутое управление расстоянием камеры с настраиваемыми параметрами"
    L["MaxCamEnhanced Settings"] = "Настройки MaxCamEnhanced"

    -- Main settings
    L["Enable Camera Distance"] = "Включить расстояние камеры"
    L["Enable camera distance extension (can be toggled on/off)"] = "Включить расширение расстояния камеры (можно включать/выключать)"
    L["Auto Apply Changes"] = "Автоприменение изменений"
    L["Automatically apply settings when you change them"] = "Автоматически применять настройки при их изменении"
    L["Unify Camera Distance"] = "Унифицировать расстояние камеры"
    L["Set the same camera distance for all characters (recommended for consistent experience)"] = "Установить одинаковое расстояние камеры для всех персонажей (рекомендуется для единообразного опыта)"
    L["Auto-Detect Optimal Distance"] = "Автоопределение оптимального расстояния"
    L["Automatically detect optimal camera distance based on character's current camera position"] = "Автоматически настраивать расстояние камеры в зависимости от текущей локации (помещения/улица, подземелья, PvP зоны)"


    -- Sections
    L["Camera Distance Settings"] = "Настройки расстояния камеры"
    L["Actions"] = "Действия"

    -- Camera settings
    L["Camera Distance"] = "Расстояние камеры"
    L["Controls camera distance from player (1-100)"] = "Управляет расстоянием камеры от игрока (1-100)"
    L["Apply on Zone Change"] = "Применять при смене зоны"
    L["Automatically apply camera distance when entering new zones"] = "Автоматически применять расстояние камеры при входе в новые зоны"





    -- Actions
    L["Apply Settings Now"] = "Применить настройки сейчас"
    L["Apply current camera distance settings immediately"] = "Немедленно применить текущие настройки расстояния камеры (включая автоопределение, если включено)"
    L["Reset to Defaults"] = "Сбросить к умолчанию"
    L["Reset all settings to default values (distance: 30, enabled, unified)"] = "Сбросить все настройки к умолчанию (расстояние: 30, включено, унифицировано). Режим унификации обеспечивает единообразный опыт камеры для всех персонажей."



    -- Messages
    L["loaded! Type /maxcam config for settings."] = "загружен! Введите /maxcam config для настроек."
    L["Commands:"] = "Команды:"
    L["Open settings"] = "Открыть настройки"
    L["Apply current settings"] = "Применить текущие настройки"
    L["Reset to defaults"] = "Сбросить к умолчанию"
    L["Show this help"] = "Показать эту справку"
    L["Unknown command. Type '/maxcam help' for available commands."] = "Неизвестная команда. Введите '/maxcam help' для списка доступных команд."
    L["Camera distance applied:"] = "Расстояние камеры применено:"
    L["Camera distance disabled"] = "Расстояние камеры отключено"

    -- Camera Pitch Settings
    L["Camera Pitch Settings"] = "Настройки наклона камеры"
    L["Control Camera Pitch"] = "Управлять наклоном камеры"
    L["Enable camera pitch (angle) control - may not work reliably in WoW 3.3.5a"] = "Включить управление наклоном (углом) камеры - может работать нестабильно в WoW 3.3.5a"
    L["Camera Pitch"] = "Наклон камеры"
    L["Camera pitch angle in degrees (-30 to 60, default: 20)"] = "Угол наклона камеры в градусах (-30 до 60, по умолчанию: 20)"
    L["(controlled by addon)"] = "(управляется аддоном)"
    L["(not controlled)"] = "(не управляется)"

    -- Tabs
    L["General"] = "Основные"
    L["Auto-Detection"] = "Автоопределение"

    -- Auto-Detection Settings
    L["Location-Based Distances"] = "Расстояния по типу локации"
    L["Instance Distance"] = "Расстояние в инстансах"
    L["Camera distance for dungeons and raids (20-30 recommended)"] = "Расстояние камеры для подземелий и рейдов (рекомендуется 20-30)"
    L["Indoor Distance"] = "Расстояние в помещениях"
    L["Camera distance for indoor locations like buildings and caves (15-25 recommended)"] = "Расстояние камеры для помещений, зданий и пещер (рекомендуется 15-25)"
    L["Outdoor Distance"] = "Расстояние на улице"
    L["Camera distance for outdoor areas and open world (35-50 recommended)"] = "Расстояние камеры для открытых областей и мира (рекомендуется 35-50)"
    L["PvP Distance"] = "Расстояние в PvP"
    L["Camera distance for PvP zones for maximum awareness (50 recommended)"] = "Расстояние камеры для PvP зон для максимального обзора (рекомендуется 50)"
    L["City Distance"] = "Расстояние в городах"
    L["Camera distance for major cities (25-30 recommended)"] = "Расстояние камеры для крупных городов (рекомендуется 25-30)"

    L["Zone Overrides"] = "Переопределения зон"
    L["Specific distance settings for individual zones (overrides location-based settings)"] = "Специфические настройки расстояния для отдельных зон (переопределяют настройки по типу локации)"

    L["Auto-Detection Settings"] = "Настройки автоопределения"
    L["Configure automatic camera distance detection based on location type and specific zones"] = "Настройка автоматического определения расстояния камеры на основе типа локации и специфических зон"

    -- Current Values (restored)
    L["Current WTF Values:"] = "Текущие значения WTF:"
    L["Current Camera Distance:"] = "Текущее расстояние камеры:"
    L["(actual zoom level)"] = "(сохраненная позиция зума)"
    L["Camera Saved Distance:"] = "Сохраненное расстояние камеры:"
    L["(default position)"] = "(позиция по умолчанию)"
    L["Camera Max Distance:"] = "Максимальное расстояние камеры:"
    L["(zoom out limit)"] = "(лимит отдаления)"
    L["Camera Pitch:"] = "Наклон камеры:"
    L["(controlled by addon)"] = "(управляется аддоном)"
    L["(not controlled)"] = "(не управляется)"
    L["Camera Distance Setting:"] = "Настройка расстояния камеры:"
    L["(both position and zoom limit)"] = "(позиция и лимит зума)"
    L["Settings look correct"] = "Настройки выглядят корректно"
    L["(Values are automatically unified on login)"] = "(Значения автоматически унифицируются при входе)"
    L["(Enable unification to standardize values)"] = "(Включите унификацию для стандартизации значений)"
end

-- Export localization table
MaxCamEnhanced_Locale = L
