-- MaxCamEnhanced Localization
-- Supports English and Russian

local L = {}

-- Default English strings
L["MaxCamEnhanced"] = "MaxCamEnhanced"
L["Advanced camera distance control with configurable settings"] = "Advanced camera distance control with configurable settings"
L["MaxCamEnhanced Settings"] = "MaxCamEnhanced Settings"

-- Main settings
L["Enable Camera Distance"] = "Enable Camera Distance"
L["Enable camera distance extension (can be toggled on/off)"] = "Enable camera distance extension (can be toggled on/off)"
L["Auto Apply Changes"] = "Auto Apply Changes"
L["Automatically apply settings when you change them"] = "Automatically apply settings when you change them"
L["Unify Camera Distance"] = "Unify Camera Distance"
L["Set the same camera distance for all characters (recommended for consistent experience)"] = "Set the same camera distance for all characters (recommended for consistent experience)"
L["Auto-Detect Optimal Distance"] = "Auto-Detect Optimal Distance"
L["Automatically detect optimal camera distance based on character's current camera position"] = "Automatically detect optimal camera distance based on character's current camera position"

-- Sections
L["Camera Distance Settings"] = "Camera Distance Settings"
L["Actions"] = "Actions"

-- Camera settings
L["Camera Distance"] = "Camera Distance"
L["Controls camera distance from player (1-100)"] = "Controls camera distance from player (1-100)"
L["Apply on Zone Change"] = "Apply on Zone Change"
L["Automatically apply camera distance when entering new zones"] = "Automatically apply camera distance when entering new zones"



-- Current Values
L["Current WTF Values"] = "Current WTF Values"
L["Current WTF Values:"] = "Current WTF Values:"
L["Camera Saved Distance:"] = "Camera Saved Distance:"
L["Camera Max Distance:"] = "Camera Max Distance:"
L["Camera Pitch:"] = "Camera Pitch:"
L["(read-only, not controlled)"] = "(read-only, not controlled)"

-- Actions
L["Apply Settings Now"] = "Apply Settings Now"
L["Apply current camera distance settings immediately"] = "Apply current camera distance settings immediately"
L["Reset to Defaults"] = "Reset to Defaults"
L["Reset all settings to default values (distance: 30, enabled, unified)"] = "Reset all settings to default values (distance: 30, enabled, unified). Unify mode provides consistent camera experience across all characters."

-- Status messages
L["Camera Distance Setting:"] = "Camera Distance Setting:"
L["(both position and zoom limit)"] = "(both position and zoom limit)"
L["Settings look correct"] = "Settings look correct"
L["(Values are automatically unified on login)"] = "(Values are automatically unified on login)"
L["(Enable unification to standardize values)"] = "(Enable unification to standardize values)"

-- Messages
L["loaded! Type /maxcam config for settings."] = "loaded! Type /maxcam config for settings."
L["Commands:"] = "Commands:"
L["Open settings"] = "Open settings"
L["Apply current settings"] = "Apply current settings"
L["Reset to defaults"] = "Reset to defaults"
L["Show this help"] = "Show this help"
L["Unknown command. Type '/maxcam help' for available commands."] = "Unknown command. Type '/maxcam help' for available commands."
L["Camera distance applied:"] = "Camera distance applied:"
L["Camera distance disabled"] = "Camera distance disabled"

-- Russian localization
if GetLocale() == "ruRU" then
    L["MaxCamEnhanced"] = "MaxCamEnhanced"
    L["Advanced camera distance control with configurable settings"] = "Продвинутое управление расстоянием камеры с настраиваемыми параметрами"
    L["MaxCamEnhanced Settings"] = "Настройки MaxCamEnhanced"

    -- Main settings
    L["Enable Camera Distance"] = "Включить расстояние камеры"
    L["Enable camera distance extension (can be toggled on/off)"] = "Включить расширение расстояния камеры (можно включать/выключать)"
    L["Auto Apply Changes"] = "Автоприменение изменений"
    L["Automatically apply settings when you change them"] = "Автоматически применять настройки при их изменении"
    L["Unify Camera Distance"] = "Унифицировать расстояние камеры"
    L["Set the same camera distance for all characters (recommended for consistent experience)"] = "Установить одинаковое расстояние камеры для всех персонажей (рекомендуется для единообразного опыта)"
    L["Auto-Detect Optimal Distance"] = "Автоопределение оптимального расстояния"
    L["Automatically detect optimal camera distance based on character's current camera position"] = "Автоматически определять оптимальное расстояние камеры на основе текущей позиции камеры персонажа"

    -- Sections
    L["Camera Distance Settings"] = "Настройки расстояния камеры"
    L["Actions"] = "Действия"

    -- Camera settings
    L["Camera Distance"] = "Расстояние камеры"
    L["Controls camera distance from player (1-100)"] = "Управляет расстоянием камеры от игрока (1-100)"
    L["Apply on Zone Change"] = "Применять при смене зоны"
    L["Automatically apply camera distance when entering new zones"] = "Автоматически применять расстояние камеры при входе в новые зоны"



    -- Current Values
    L["Current WTF Values"] = "Текущие значения WTF"
    L["Current WTF Values:"] = "Текущие значения WTF:"
    L["Camera Saved Distance:"] = "Сохраненное расстояние камеры:"
    L["Camera Max Distance:"] = "Максимальное расстояние камеры:"
    L["Camera Pitch:"] = "Наклон камеры:"
    L["(read-only, not controlled)"] = "(только для чтения, не управляется)"

    -- Actions
    L["Apply Settings Now"] = "Применить настройки сейчас"
    L["Apply current camera distance settings immediately"] = "Немедленно применить текущие настройки расстояния камеры"
    L["Reset to Defaults"] = "Сбросить к умолчанию"
    L["Reset all settings to default values (distance: 30, enabled, unified)"] = "Сбросить все настройки к умолчанию (расстояние: 30, включено, унифицировано). Режим унификации обеспечивает единообразный опыт камеры для всех персонажей."

    -- Status messages
    L["Camera Distance Setting:"] = "Настройка расстояния камеры:"
    L["(both position and zoom limit)"] = "(позиция и лимит зума)"
    L["Settings look correct"] = "Настройки выглядят корректно"
    L["(Values are automatically unified on login)"] = "(Значения автоматически унифицируются при входе)"
    L["(Enable unification to standardize values)"] = "(Включите унификацию для стандартизации значений)"

    -- Messages
    L["loaded! Type /maxcam config for settings."] = "загружен! Введите /maxcam config для настроек."
    L["Commands:"] = "Команды:"
    L["Open settings"] = "Открыть настройки"
    L["Apply current settings"] = "Применить текущие настройки"
    L["Reset to defaults"] = "Сбросить к умолчанию"
    L["Show this help"] = "Показать эту справку"
    L["Unknown command. Type '/maxcam help' for available commands."] = "Неизвестная команда. Введите '/maxcam help' для списка доступных команд."
    L["Camera distance applied:"] = "Расстояние камеры применено:"
    L["Camera distance disabled"] = "Расстояние камеры отключено"
end

-- Export localization table
MaxCamEnhanced_Locale = L
