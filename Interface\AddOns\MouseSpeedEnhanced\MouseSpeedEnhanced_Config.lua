MouseSpeedEnhanced = MouseSpeedEnhanced or {}
local AceConfig = LibStub("AceConfig-3.0")
local AceConfigDialog = LibStub("AceConfigDialog-3.0")
local AceConfigRegistry = LibStub("AceConfigRegistry-3.0")
local L = MouseSpeedEnhanced_Locale or {}

local options = nil

local function get(info)
    if not MouseSpeedEnhanced.db then
        return nil
    end
    return MouseSpeedEnhanced.db.profile[info[#info]]
end

local function set(info, value)
    if not MouseSpeedEnhanced.db then
        return
    end
    MouseSpeedEnhanced.db.profile[info[#info]] = value

    -- Always save settings to database
    if MouseSpeedEnhanced.db then
        MouseSpeedEnhanced.db:SetProfile(MouseSpeedEnhanced.db:GetCurrentProfile())
    end

    -- Apply settings immediately if auto-apply is enabled OR if enableOnLogin is being changed
    if MouseSpeedEnhanced.db.profile.autoApply or info[#info] == "enableOnLogin" then
        MouseSpeedEnhanced:ApplySettings()
        MouseSpeedEnhanced:UpdateCurrentValues()
    end
end

-- Special set function for sliders that applies settings only when slider is released
local function setSlider(info, value)
    if not MouseSpeedEnhanced.db then
        return
    end
    MouseSpeedEnhanced.db.profile[info[#info]] = value

    -- Always save settings to database
    if MouseSpeedEnhanced.db then
        MouseSpeedEnhanced.db:SetProfile(MouseSpeedEnhanced.db:GetCurrentProfile())
    end

    -- Apply settings immediately if auto-apply is enabled (sliders apply on release)
    if MouseSpeedEnhanced.db.profile.autoApply then
        MouseSpeedEnhanced:ApplySettings()
        MouseSpeedEnhanced:UpdateCurrentValues()
    end
end

local function createOptions()
    if options then
        return options
    end
    
    options = {
        type = "group",
        name = "MouseSpeedEnhanced",
        desc = L["Advanced mouse and camera sensitivity control"] or "Advanced mouse and camera sensitivity control",
        args = {
            header = {
                type = "header",
                name = L["MouseSpeedEnhanced Settings"] or "MouseSpeedEnhanced Settings",
                order = 1,
            },
            -- Main toggles at the top for convenience
            autoApply = {
                type = "toggle",
                name = L["Auto Apply Changes"] or "Auto Apply Changes",
                desc = L["Automatically apply settings when you change them"] or "Automatically apply settings when you change them",
                order = 2,
                get = get,
                set = set,
            },
            enableOnLogin = {
                type = "toggle",
                name = L["Enable Camera Settings"] or "Enable Camera Settings",
                desc = L["Enable camera speed settings (mouse speed is always active)"] or "Enable camera speed settings (mouse speed is always active)",
                order = 3,
                get = get,
                set = set,
            },
            spacer1 = {
                type = "header",
                name = L["Mouse Settings"] or "Mouse Settings",
                order = 4,
            },
            mouseSpeed = {
                type = "range",
                name = L["Mouse Sensitivity"] or "Mouse Sensitivity",
                desc = L["Controls mouse sensitivity (lower = slower, higher = faster). Always applied on login."] or "Controls mouse sensitivity (lower = slower, higher = faster). Always applied on login.",
                min = 0.01,
                max = 10.0,
                step = 0.01,
                bigStep = 0.1,
                order = 5,
                get = get,
                set = setSlider,
            },
            spacer2 = {
                type = "header",
                name = L["Camera Settings"] or "Camera Settings",
                order = 6,
            },
            cameraYawMoveSpeed = {
                type = "range",
                name = L["Camera Horizontal Speed"] or "Camera Horizontal Speed",
                desc = L["Controls horizontal camera movement speed (left/right)"] or "Controls horizontal camera movement speed (left/right)",
                min = 1,
                max = 180,
                step = 1,
                bigStep = 5,
                order = 7,
                get = get,
                set = setSlider,
                disabled = function() return not MouseSpeedEnhanced.db.profile.enableOnLogin end,
            },
            cameraPitchMoveSpeed = {
                type = "range",
                name = L["Camera Vertical Speed"] or "Camera Vertical Speed",
                desc = L["Controls vertical camera movement speed (up/down)"] or "Controls vertical camera movement speed (up/down)",
                min = 1,
                max = 180,
                step = 1,
                bigStep = 5,
                order = 8,
                get = get,
                set = setSlider,
                disabled = function() return not MouseSpeedEnhanced.db.profile.enableOnLogin end,
            },
            spacer3 = {
                type = "header",
                name = L["Actions"] or "Actions",
                order = 9,
            },

            reset = {
                type = "execute",
                name = L["Reset to Defaults"] or "Reset to Defaults",
                desc = L["Reset all settings to default values (mouse: 0.17, camera: 55)"] or "Reset all settings to default values (mouse: 0.17, camera: 55)",
                order = 11,
                func = function()
                    MouseSpeedEnhanced:ResetToDefaults()
                    MouseSpeedEnhanced:UpdateCurrentValues()
                end,
            },

            spacer3 = {
                type = "header",
                name = L["Current WTF Values:"] or "Current WTF Values:",
                order = 12,
            },
            currentValues = {
                type = "description",
                name = function()
                    local currentMouseSpeed = GetCVar("mouseSpeed") or "Unknown"
                    local currentCameraSensitivity = GetCVar("cameraYawMoveSpeed") or "Unknown"
                    local currentCameraPitch = GetCVar("cameraPitchMoveSpeed") or "Unknown"

                    local text = "|cFFFFFF00" .. (L["Current WTF Values:"] or "Current WTF Values:") .. "|r\n"
                    text = text .. "• " .. (L["Mouse Speed:"] or "Mouse Speed:") .. " |cFF00FF00" .. currentMouseSpeed .. "|r " .. (L["(controls mouse sensitivity)"] or "(controls mouse sensitivity)") .. "\n"
                    text = text .. "• " .. (L["Camera Yaw Speed:"] or "Camera Yaw Speed:") .. " |cFF00FF00" .. currentCameraSensitivity .. "|r " .. (L["(controls horizontal camera)"] or "(controls horizontal camera)") .. "\n"
                    text = text .. "• " .. (L["Camera Pitch Speed:"] or "Camera Pitch Speed:") .. " |cFF00FF00" .. currentCameraPitch .. "|r " .. (L["(controls vertical camera)"] or "(controls vertical camera)") .. "\n\n"

                    if MouseSpeedEnhanced.db and MouseSpeedEnhanced.db.profile then
                        if MouseSpeedEnhanced.db.profile.autoApply then
                            text = text .. "|cFF88FF88" .. (L["Settings look correct"] or "Settings look correct") .. "|r\n"
                            text = text .. "|cFF888888" .. (L["(Values are automatically applied)"] or "(Values are automatically applied)") .. "|r"
                        else
                            text = text .. "|cFFFFAA00" .. (L["Auto-apply is disabled"] or "Auto-apply is disabled") .. "|r\n"
                            text = text .. "|cFF888888" .. (L["(Enable auto-apply to automatically apply settings)"] or "(Enable auto-apply to automatically apply settings)") .. "|r"
                        end
                    end

                    return text
                end,
                order = 13,
            },
        }
    }

    return options
end

-- Configuration initialization function (called after addon load)
function MouseSpeedEnhanced.InitializeConfig()
    if MouseSpeedEnhanced.configInitialized then
        return -- Prevent duplicate registration
    end

    local options = createOptions()
    -- Unregister any existing registration first
    pcall(function() AceConfigDialog:Close("MouseSpeedEnhanced") end)

    AceConfig:RegisterOptionsTable("MouseSpeedEnhanced", options)
    AceConfigDialog:AddToBlizOptions("MouseSpeedEnhanced", "MouseSpeedEnhanced")

    -- Hook interface close to apply settings
    local originalHide = InterfaceOptionsFrame.Hide
    InterfaceOptionsFrame.Hide = function(self)
        -- Apply settings when interface is closed
        if MouseSpeedEnhanced and MouseSpeedEnhanced.ApplySettings then
            MouseSpeedEnhanced:ApplySettings()
        end
        originalHide(self)
    end

    MouseSpeedEnhanced.configInitialized = true
end
