-- Test script for MaxCamEnhanced functions
-- This file can be loaded in-game to test functionality

local function TestAutoDetection()
    print("=== Testing Auto-Detection ===")
    
    -- Test GetCameraZoom function
    local zoom = GetCameraZoom()
    print("GetCameraZoom():", zoom or "nil")
    
    -- Test GetCVar for camera distance
    local savedDist = GetCVar("cameraSavedDistance")
    print("cameraSavedDistance:", savedDist or "nil")
    
    -- Test auto-detection function
    if MaxCamEnhanced and MaxCamEnhanced.AutoDetectOptimalDistance then
        local optimal = MaxCamEnhanced:AutoDetectOptimalDistance()
        print("Auto-detected optimal distance:", optimal)
    else
        print("MaxCamEnhanced not loaded or function not found")
    end
end

local function TestCameraPitch()
    print("=== Testing Camera Pitch ===")
    
    -- Test current pitch
    local currentPitch = GetCVar("cameraSavedPitch")
    print("Current cameraSavedPitch:", currentPitch or "nil")
    
    -- Test setting pitch
    print("Setting pitch to 25...")
    SetCVar("cameraSavedPitch", "25")
    
    -- Check if it changed
    local newPitch = GetCVar("cameraSavedPitch")
    print("New cameraSavedPitch:", newPitch or "nil")
    
    -- Test if pitch actually affects camera
    print("Try moving camera to see if pitch is applied")
end

local function TestCameraFunctions()
    print("=== Testing Camera Functions in WoW 3.3.5a ===")
    
    -- Test available camera functions
    local functions = {
        "GetCameraZoom",
        "SetCameraZoom", 
        "GetCVar",
        "SetCVar",
        "ConsoleExec"
    }
    
    for _, funcName in ipairs(functions) do
        local func = _G[funcName]
        if func then
            print(funcName .. ": Available")
        else
            print(funcName .. ": NOT AVAILABLE")
        end
    end
    
    TestAutoDetection()
    TestCameraPitch()
end

-- Create slash command for testing
SLASH_TESTMAXCAM1 = "/testmaxcam"
SlashCmdList["TESTMAXCAM"] = function(msg)
    TestCameraFunctions()
end

print("MaxCamEnhanced Test Functions loaded. Use /testmaxcam to run tests.")
