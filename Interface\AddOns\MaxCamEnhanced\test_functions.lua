-- Test script for MaxCamEnhanced functions
-- This file can be loaded in-game to test functionality

local function TestAutoDetection()
    print("=== Testing Location-Based Auto-Detection ===")

    -- Test location information
    local zone = GetZoneText() or "Unknown"
    local subzone = GetSubZoneText() or "Unknown"
    local isIndoors = IsIndoors()
    local isInInstance = IsInInstance()

    print("Zone:", zone)
    print("Subzone:", subzone)
    print("Is Indoors:", isIndoors and "YES" or "NO")
    print("Is Instance:", isInInstance and "YES" or "NO")

    -- Determine location type
    local locationType
    if isInInstance then
        locationType = "INSTANCE (Dungeon/Raid)"
    elseif isIndoors then
        locationType = "INDOOR (Building/Cave)"
    else
        locationType = "OUTDOOR (Open World)"
    end
    print("Location Type:", locationType)

    -- Test auto-detection function
    if MaxCamEnhanced and MaxCamEnhanced.AutoDetectOptimalDistance then
        local optimal = MaxCamEnhanced:AutoDetectOptimalDistance()
        print("Auto-detected optimal distance:", optimal)

        -- Show what would change
        if MaxCamEnhanced.db and MaxCamEnhanced.db.profile then
            local current = MaxCamEnhanced.db.profile.cameraDistance
            print("Current setting:", current)
            print("Would change by:", optimal - current)

            -- Show reasoning
            if isInInstance then
                print("Reasoning: Instance detected - using close camera for better control")
            elseif isIndoors then
                print("Reasoning: Indoor location - using close camera for tight spaces")
            else
                print("Reasoning: Outdoor location - using far camera for better view")
            end
        end
    else
        print("MaxCamEnhanced not loaded or function not found")
    end
end

local function TestCameraPitch()
    print("=== Testing Camera Pitch ===")
    
    -- Test current pitch
    local currentPitch = GetCVar("cameraSavedPitch")
    print("Current cameraSavedPitch:", currentPitch or "nil")
    
    -- Test setting pitch
    print("Setting pitch to 25...")
    SetCVar("cameraSavedPitch", "25")
    
    -- Check if it changed
    local newPitch = GetCVar("cameraSavedPitch")
    print("New cameraSavedPitch:", newPitch or "nil")
    
    -- Test if pitch actually affects camera
    print("Try moving camera to see if pitch is applied")
end

local function TestCameraFunctions()
    print("=== Testing Camera Functions in WoW 3.3.5a ===")
    
    -- Test available camera functions
    local functions = {
        "GetCVar",
        "SetCVar",
        "ConsoleExec"
    }
    
    for _, funcName in ipairs(functions) do
        local func = _G[funcName]
        if func then
            print(funcName .. ": Available")
        else
            print(funcName .. ": NOT AVAILABLE")
        end
    end
    
    TestAutoDetection()
    TestCameraPitch()
end

-- Create slash command for testing
SLASH_TESTMAXCAM1 = "/testmaxcam"
SlashCmdList["TESTMAXCAM"] = function(msg)
    TestCameraFunctions()
end

print("MaxCamEnhanced Test Functions loaded. Use /testmaxcam to run tests.")
