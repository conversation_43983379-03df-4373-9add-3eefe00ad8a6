-- Test script for MaxCamEnhanced functions
-- This file can be loaded in-game to test functionality

local function TestAutoDetection()
    print("=== Testing Configurable Auto-Detection ===")

    -- Test location information
    local zone = GetZoneText() or "Unknown"
    local subzone = GetSubZoneText() or "Unknown"
    local isIndoors = IsIndoors()
    local isInInstance = IsInInstance()

    print("Zone:", zone)
    print("Subzone:", subzone)
    print("Is Indoors:", isIndoors and "YES" or "NO")
    print("Is Instance:", isInInstance and "YES" or "NO")

    -- Test auto-detection settings
    if MaxCamEnhanced and MaxCamEnhanced.db and MaxCamEnhanced.db.profile.autoDetectSettings then
        local settings = MaxCamEnhanced.db.profile.autoDetectSettings

        print("\n=== Current Auto-Detection Settings ===")
        print("Instance Distance:", settings.instanceDistance)
        print("Indoor Distance:", settings.indoorDistance)
        print("Outdoor Distance:", settings.outdoorDistance)
        print("PvP Distance:", settings.pvpDistance)
        print("City Distance:", settings.cityDistance)

        print("\n=== Zone Overrides ===")
        for zoneName, distance in pairs(settings.zoneOverrides) do
            print(string.format("%s: %d", zoneName, distance))
        end

        -- Test auto-detection function
        if MaxCamEnhanced.AutoDetectOptimalDistance then
            local optimal = MaxCamEnhanced:AutoDetectOptimalDistance()
            print("\n=== Detection Result ===")
            print("Auto-detected optimal distance:", optimal)

            -- Show reasoning
            if settings.zoneOverrides[zone] then
                print("Reasoning: Zone override found for", zone)
            elseif isInInstance then
                print("Reasoning: Instance detected - using instanceDistance setting")
            elseif isIndoors then
                print("Reasoning: Indoor location - using indoorDistance setting")
            else
                print("Reasoning: Outdoor location - using outdoorDistance setting")
            end

            -- Show what would change
            if MaxCamEnhanced.db.profile then
                local current = MaxCamEnhanced.db.profile.cameraDistance
                print("Current setting:", current)
                print("Would change by:", optimal - current)
            end


        end
    else
        print("MaxCamEnhanced auto-detection settings not found")
    end
end



local function TestCameraPitch()
    print("=== Testing Camera Pitch ===")
    
    -- Test current pitch
    local currentPitch = GetCVar("cameraSavedPitch")
    print("Current cameraSavedPitch:", currentPitch or "nil")
    
    -- Test setting pitch
    print("Setting pitch to 25...")
    SetCVar("cameraSavedPitch", "25")
    
    -- Check if it changed
    local newPitch = GetCVar("cameraSavedPitch")
    print("New cameraSavedPitch:", newPitch or "nil")
    
    -- Test if pitch actually affects camera
    print("Try moving camera to see if pitch is applied")
end

local function TestCameraFunctions()
    print("=== Testing Camera Functions in WoW 3.3.5a ===")
    
    -- Test available camera functions
    local functions = {
        "GetCVar",
        "SetCVar",
        "ConsoleExec"
    }
    
    for _, funcName in ipairs(functions) do
        local func = _G[funcName]
        if func then
            print(funcName .. ": Available")
        else
            print(funcName .. ": NOT AVAILABLE")
        end
    end
    
    TestAutoDetection()
    TestCameraPitch()
    TestConfigInterface()
end

local function TestConfigInterface()
    print("\n=== Testing Configuration Interface ===")

    if MaxCamEnhanced and MaxCamEnhanced.InitializeConfig then
        print("Initializing configuration...")
        MaxCamEnhanced.InitializeConfig()
        print("Configuration initialized successfully!")

        -- Test opening interface
        print("Opening interface...")
        InterfaceOptionsFrame_OpenToCategory("MaxCamEnhanced")
        print("Interface should be open now")

        -- Test tab structure
        print("\n=== Testing Tab Structure ===")
        if MaxCamEnhanced.db and MaxCamEnhanced.db.profile then
            print("Database found - tabs should work")
            print("General tab: Basic settings")
            print("Auto-Detection tab: Location-based settings")

            -- Test auto-detection settings
            if MaxCamEnhanced.db.profile.autoDetectSettings then
                print("Auto-detection settings found:")
                local settings = MaxCamEnhanced.db.profile.autoDetectSettings
                print("- Instance Distance:", settings.instanceDistance)
                print("- Indoor Distance:", settings.indoorDistance)
                print("- Outdoor Distance:", settings.outdoorDistance)
                print("- PvP Distance:", settings.pvpDistance)
                print("- City Distance:", settings.cityDistance)
                print("- Zone Overrides:", #settings.zoneOverrides, "zones configured")
            else
                print("Auto-detection settings not found")
            end
        else
            print("Database not found - tabs may not work properly")
        end
    else
        print("MaxCamEnhanced not loaded or InitializeConfig not found")
    end
end

-- Create slash command for testing
SLASH_TESTMAXCAM1 = "/testmaxcam"
SlashCmdList["TESTMAXCAM"] = function(msg)
    TestCameraFunctions()
end

print("MaxCamEnhanced Test Functions loaded. Use /testmaxcam to run tests.")
