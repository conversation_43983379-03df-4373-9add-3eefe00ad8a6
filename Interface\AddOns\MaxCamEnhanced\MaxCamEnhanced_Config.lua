MaxCamEnhanced = MaxCamEnhanced or {}
local AceConfig = LibStub("AceConfig-3.0")
local AceConfigDialog = LibStub("AceConfigDialog-3.0")
local AceConfigRegistry = LibStub("AceConfigRegistry-3.0")
local L = MaxCamEnhanced_Locale or {}

local options = nil

local function get(info)
    if not MaxCamEnhanced or not MaxCamEnhanced.db then
        return nil
    end
    local key = info[#info]
    local value = MaxCamEnhanced.db.profile[key]
    return value
end

local function set(info, value)
    if not MaxCamEnhanced or not MaxCamEnhanced.db then
        return
    end
    MaxCamEnhanced.db.profile[info[#info]] = value

    -- Apply settings immediately if auto-apply is enabled, but with throttling
    if MaxCamEnhanced.db.profile.autoApply then
        -- Throttle rapid changes
        if MaxCamEnhanced.applyTimer then
            MaxCamEnhanced.applyTimer:Cancel()
        end
        MaxCamEnhanced.applyTimer = C_Timer.NewTimer(0.5, function()
            MaxCamEnhanced:ApplySettings()
            MaxCamEnhanced.applyTimer = nil
        end)
    end
end

local function createOptions()
    if options then
        return options
    end

    options = {
        type = "group",
        name = "MaxCamEnhanced",
        desc = L["Advanced camera distance control with configurable settings"] or "Advanced camera distance control with configurable settings",
        childGroups = "tab",
        args = {
            general = {
                type = "group",
                name = L["General"] or "General",
                order = 1,
                args = getGeneralOptions()
            },
            autoDetection = {
                type = "group",
                name = L["Auto-Detection"] or "Auto-Detection",
                order = 2,
                args = getAutoDetectionOptions()
            }
        }
    }
    return options
end

local function getGeneralOptions()
    return {
            header = {
                type = "header",
                name = L["MaxCamEnhanced Settings"] or "MaxCamEnhanced Settings",
                order = 1,
            },
            -- Main toggles at the top for convenience
            enabled = {
                type = "toggle",
                name = L["Enable Camera Distance"] or "Enable Camera Distance",
                desc = L["Enable camera distance extension (can be toggled on/off)"] or "Enable camera distance extension (can be toggled on/off)",
                order = 2,
                get = get,
                set = function(info, value)
                    set(info, value)
                    if value then
                        MaxCamEnhanced:ApplySettings()
                    end

                end,
            },
            autoApply = {
                type = "toggle",
                name = L["Auto Apply Changes"] or "Auto Apply Changes",
                desc = L["Automatically apply settings when you change them"] or "Automatically apply settings when you change them",
                order = 3,
                get = get,
                set = set,
            },
            unifyCameraDistance = {
                type = "toggle",
                name = L["Unify Camera Distance"] or "Unify Camera Distance",
                desc = L["Set the same camera distance for all characters (recommended for consistent experience)"] or "Set the same camera distance for all characters (recommended for consistent experience)",
                order = 4,
                get = get,
                set = set,
            },
            autoDetectDistance = {
                type = "toggle",
                name = L["Auto-Detect Optimal Distance"] or "Auto-Detect Optimal Distance",
                desc = L["Automatically detect optimal camera distance based on character's current camera position"] or "Automatically detect optimal camera distance based on character's saved camera settings",
                order = 5,
                get = get,
                set = function(info, value)
                    set(info, value)
                    -- Apply settings immediately when auto-detection is toggled
                    if MaxCamEnhanced.db.profile.enabled then
                        MaxCamEnhanced:ApplySettings()

                    end
                end,
            },
            spacer1 = {
                type = "header",
                name = L["Camera Distance Settings"] or "Camera Distance Settings",
                order = 6,
            },
            cameraDistance = {
                type = "range",
                name = L["Camera Distance"] or "Camera Distance",
                desc = L["Camera distance - this will be both your default position AND maximum zoom distance"] or "Camera distance - this will be both your default position AND maximum zoom distance",
                min = 1,
                max = 100,
                step = 1,
                bigStep = 5,
                order = 7,
                get = get,
                set = function(info, value)
                    -- Set both camera distance and unified distance to the same value
                    MaxCamEnhanced.db.profile.cameraDistance = value
                    MaxCamEnhanced.db.profile.unifiedSavedDistance = value

                    -- Apply settings immediately if auto-apply is enabled, but with throttling
                    if MaxCamEnhanced.db.profile.autoApply then
                        -- Throttle rapid changes
                        if MaxCamEnhanced.applyTimer then
                            MaxCamEnhanced.applyTimer:Cancel()
                        end
                        MaxCamEnhanced.applyTimer = C_Timer.NewTimer(0.5, function()
                            MaxCamEnhanced:ApplySettings()
                            MaxCamEnhanced.applyTimer = nil
                        end)
                    end
                end,
                disabled = function() return not MaxCamEnhanced.db.profile.enabled or MaxCamEnhanced.db.profile.autoDetectDistance end,
            },



            spacer2 = {
                type = "header",
                name = L["Camera Pitch Settings"] or "Camera Pitch Settings",
                order = 8,
            },
            controlCameraPitch = {
                type = "toggle",
                name = L["Control Camera Pitch"] or "Control Camera Pitch",
                desc = function()
                    local baseDesc = L["Enable camera pitch (angle) control - may not work reliably in WoW 3.3.5a"] or "Enable camera pitch (angle) control - may not work reliably in WoW 3.3.5a"
                    if MaxCamEnhanced and MaxCamEnhanced.pitchSupported == false then
                        return baseDesc .. "\n|cFFFF0000Camera pitch control is not supported in this WoW version.|r"
                    elseif MaxCamEnhanced and MaxCamEnhanced.pitchSupported == true then
                        return baseDesc .. "\n|cFF00FF00Camera pitch control is supported.|r"
                    else
                        return baseDesc
                    end
                end,
                order = 9,
                get = get,
                set = function(info, value)
                    set(info, value)
                    if MaxCamEnhanced.db.profile.enabled and MaxCamEnhanced.db.profile.autoApply then
                        MaxCamEnhanced:ApplySettings()

                    end
                end,
                disabled = function()
                    return not MaxCamEnhanced.db.profile.enabled or (MaxCamEnhanced.pitchSupported == false)
                end,
            },
            cameraPitch = {
                type = "range",
                name = L["Camera Pitch"] or "Camera Pitch",
                desc = L["Camera pitch angle in degrees (-30 to 60, default: 20)"] or "Camera pitch angle in degrees (-30 to 60, default: 20)",
                min = -30,
                max = 60,
                step = 1,
                bigStep = 5,
                order = 10,
                get = get,
                set = function(info, value)
                    set(info, value)
                    if MaxCamEnhanced.db.profile.enabled and MaxCamEnhanced.db.profile.autoApply and MaxCamEnhanced.db.profile.controlCameraPitch then
                        -- Throttle rapid changes
                        if MaxCamEnhanced.pitchTimer then
                            MaxCamEnhanced.pitchTimer:Cancel()
                        end
                        MaxCamEnhanced.pitchTimer = C_Timer.NewTimer(0.5, function()
                            MaxCamEnhanced:ApplySettings()
                            MaxCamEnhanced.pitchTimer = nil
                        end)
                    end
                end,
                disabled = function()
                    return not MaxCamEnhanced.db.profile.enabled or
                           not MaxCamEnhanced.db.profile.controlCameraPitch or
                           (MaxCamEnhanced.pitchSupported == false)
                end,
            },

            applyOnZoneChange = {
                type = "toggle",
                name = L["Apply on Zone Change"] or "Apply on Zone Change",
                desc = L["Automatically apply camera distance when entering new zones"] or "Automatically apply camera distance when entering new zones",
                order = 11,
                get = get,
                set = set,
                disabled = function() return not MaxCamEnhanced.db.profile.enabled end,
            },

            spacer4 = {
                type = "header",
                name = L["Actions"] or "Actions",
                order = 12,
            },

            applyNow = {
                type = "execute",
                name = L["Apply Settings Now"] or "Apply Settings Now",
                desc = L["Apply current camera distance settings immediately"] or "Apply current camera distance settings immediately",
                order = 13,
                func = function()
                    MaxCamEnhanced:ApplySettings()
                end,
                disabled = function() return not MaxCamEnhanced.db.profile.enabled end,
            },
            resetDefaults = {
                type = "execute",
                name = L["Reset to Defaults"] or "Reset to Defaults",
                desc = L["Reset all settings to default values (distance: 30, enabled, unified)"] or "Reset all settings to default values",
                order = 14,
                func = function()
                    MaxCamEnhanced.db:ResetProfile()
                    MaxCamEnhanced:ApplySettings()
                end,
            },


    }
end

local function getAutoDetectionOptions()
    local options = {
        header = {
            type = "header",
            name = L["Auto-Detection Settings"] or "Auto-Detection Settings",
            order = 1,
        },
        description = {
            type = "description",
            name = L["Configure automatic camera distance detection based on location type and specific zones"] or "Configure automatic camera distance detection based on location type and specific zones",
            order = 2,
        },

        spacer1 = {
            type = "header",
            name = L["Location-Based Distances"] or "Location-Based Distances",
            order = 3,
        },

        instanceDistance = {
            type = "range",
            name = L["Instance Distance"] or "Instance Distance",
            desc = L["Camera distance for dungeons and raids (20-30 recommended)"] or "Camera distance for dungeons and raids (20-30 recommended)",
            min = 1, max = 100, step = 1,
            order = 4,
            get = function() return MaxCamEnhanced.db.profile.autoDetectSettings.instanceDistance end,
            set = function(_, value)
                MaxCamEnhanced.db.profile.autoDetectSettings.instanceDistance = value
                if MaxCamEnhanced.db.profile.autoDetectDistance and MaxCamEnhanced.db.profile.enabled then
                    MaxCamEnhanced:ApplySettings()
                end
            end,
        },

        indoorDistance = {
            type = "range",
            name = L["Indoor Distance"] or "Indoor Distance",
            desc = L["Camera distance for indoor locations like buildings and caves (15-25 recommended)"] or "Camera distance for indoor locations like buildings and caves (15-25 recommended)",
            min = 1, max = 100, step = 1,
            order = 5,
            get = function() return MaxCamEnhanced.db.profile.autoDetectSettings.indoorDistance end,
            set = function(_, value)
                MaxCamEnhanced.db.profile.autoDetectSettings.indoorDistance = value
                if MaxCamEnhanced.db.profile.autoDetectDistance and MaxCamEnhanced.db.profile.enabled then
                    MaxCamEnhanced:ApplySettings()
                end
            end,
        },

        outdoorDistance = {
            type = "range",
            name = L["Outdoor Distance"] or "Outdoor Distance",
            desc = L["Camera distance for outdoor areas and open world (35-50 recommended)"] or "Camera distance for outdoor areas and open world (35-50 recommended)",
            min = 1, max = 100, step = 1,
            order = 6,
            get = function() return MaxCamEnhanced.db.profile.autoDetectSettings.outdoorDistance end,
            set = function(_, value)
                MaxCamEnhanced.db.profile.autoDetectSettings.outdoorDistance = value
                if MaxCamEnhanced.db.profile.autoDetectDistance and MaxCamEnhanced.db.profile.enabled then
                    MaxCamEnhanced:ApplySettings()
                end
            end,
        },

        pvpDistance = {
            type = "range",
            name = L["PvP Distance"] or "PvP Distance",
            desc = L["Camera distance for PvP zones for maximum awareness (50 recommended)"] or "Camera distance for PvP zones for maximum awareness (50 recommended)",
            min = 1, max = 100, step = 1,
            order = 7,
            get = function() return MaxCamEnhanced.db.profile.autoDetectSettings.pvpDistance end,
            set = function(_, value)
                MaxCamEnhanced.db.profile.autoDetectSettings.pvpDistance = value
                if MaxCamEnhanced.db.profile.autoDetectDistance and MaxCamEnhanced.db.profile.enabled then
                    MaxCamEnhanced:ApplySettings()
                end
            end,
        },

        cityDistance = {
            type = "range",
            name = L["City Distance"] or "City Distance",
            desc = L["Camera distance for major cities (25-30 recommended)"] or "Camera distance for major cities (25-30 recommended)",
            min = 1, max = 100, step = 1,
            order = 8,
            get = function() return MaxCamEnhanced.db.profile.autoDetectSettings.cityDistance end,
            set = function(_, value)
                MaxCamEnhanced.db.profile.autoDetectSettings.cityDistance = value
                if MaxCamEnhanced.db.profile.autoDetectDistance and MaxCamEnhanced.db.profile.enabled then
                    MaxCamEnhanced:ApplySettings()
                end
            end,
        },

        spacer2 = {
            type = "header",
            name = L["Zone Overrides"] or "Zone Overrides",
            order = 9,
        },

        zoneOverridesDesc = {
            type = "description",
            name = L["Specific distance settings for individual zones (overrides location-based settings)"] or "Specific distance settings for individual zones (overrides location-based settings)",
            order = 10,
        },
    }

    -- Add zone override controls dynamically
    if MaxCamEnhanced and MaxCamEnhanced.db and MaxCamEnhanced.db.profile.autoDetectSettings then
        local zoneOverrides = MaxCamEnhanced.db.profile.autoDetectSettings.zoneOverrides
        local order = 11

        for zoneName, distance in pairs(zoneOverrides) do
            local safeZoneName = zoneName:gsub("[^%w]", "_") -- Make safe for table key

            options[safeZoneName] = {
                type = "range",
                name = zoneName,
                desc = string.format("Camera distance for %s (current: %d)", zoneName, distance),
                min = 1, max = 100, step = 1,
                order = order,
                get = function() return MaxCamEnhanced.db.profile.autoDetectSettings.zoneOverrides[zoneName] end,
                set = function(_, value)
                    MaxCamEnhanced.db.profile.autoDetectSettings.zoneOverrides[zoneName] = value
                    if MaxCamEnhanced.db.profile.autoDetectDistance and MaxCamEnhanced.db.profile.enabled then
                        MaxCamEnhanced:ApplySettings()
                    end
                end,
            }
            order = order + 1
        end
    end

    return options
end

-- Configuration initialization function (called after addon load)
function MaxCamEnhanced.InitializeConfig()
    if MaxCamEnhanced.configInitialized then
        return -- Prevent duplicate registration
    end

    -- Ensure database is initialized
    if not MaxCamEnhanced or not MaxCamEnhanced.db then
        C_Timer.After(1, function()
            MaxCamEnhanced.InitializeConfig()
        end)
        return
    end

    local options = createOptions()
    -- Unregister any existing registration first
    pcall(function() AceConfigDialog:Close("MaxCamEnhanced") end)

    AceConfig:RegisterOptionsTable("MaxCamEnhanced", options)
    AceConfigDialog:AddToBlizOptions("MaxCamEnhanced", "MaxCamEnhanced")

    -- Hook interface close to apply settings
    local originalHide = InterfaceOptionsFrame.Hide
    InterfaceOptionsFrame.Hide = function(self)
        -- Apply settings when interface is closed
        if MaxCamEnhanced and MaxCamEnhanced.ApplySettings then
            MaxCamEnhanced:ApplySettings()
        end
        originalHide(self)
    end

    MaxCamEnhanced.configInitialized = true
end
