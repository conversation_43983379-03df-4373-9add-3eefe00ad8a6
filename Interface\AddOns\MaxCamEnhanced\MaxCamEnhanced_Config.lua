MaxCamEnhanced = MaxCamEnhanced or {}
local AceConfig = LibStub("AceConfig-3.0")
local AceConfigDialog = LibStub("AceConfigDialog-3.0")
local AceConfigRegistry = LibStub("AceConfigRegistry-3.0")
local L = MaxCamEnhanced_Locale or {}

local options = nil

local function get(info)
    if not MaxCamEnhanced or not MaxCamEnhanced.db then
        print("MaxCamEnhanced: Database not initialized!")
        return nil
    end
    local key = info[#info]
    local value = MaxCamEnhanced.db.profile[key]
    return value
end

local function set(info, value)
    if not MaxCamEnhanced or not MaxCamEnhanced.db then
        return
    end
    MaxCamEnhanced.db.profile[info[#info]] = value

    -- Apply settings immediately if auto-apply is enabled, but with throttling
    if MaxCamEnhanced.db.profile.autoApply then
        -- Throttle rapid changes
        if MaxCamEnhanced.applyTimer then
            MaxCamEnhanced.applyTimer:Cancel()
        end
        MaxCamEnhanced.applyTimer = C_Timer.NewTimer(0.5, function()
            MaxCamEnhanced:ApplySettings()
            MaxCamEnhanced.applyTimer = nil
        end)
    end
end

local function createOptions()
    if options then
        return options
    end
    
    options = {
        type = "group",
        name = "MaxCamEnhanced",
        desc = L["Advanced camera distance control with configurable settings"] or "Advanced camera distance control with configurable settings",
        args = {
            header = {
                type = "header",
                name = L["MaxCamEnhanced Settings"] or "MaxCamEnhanced Settings",
                order = 1,
            },
            -- Main toggles at the top for convenience
            enabled = {
                type = "toggle",
                name = L["Enable Camera Distance"] or "Enable Camera Distance",
                desc = L["Enable camera distance extension (can be toggled on/off)"] or "Enable camera distance extension (can be toggled on/off)",
                order = 2,
                get = get,
                set = function(info, value)
                    set(info, value)
                    if value then
                        MaxCamEnhanced:ApplySettings()
                    end
                    MaxCamEnhanced:UpdateCurrentValues()
                end,
            },
            autoApply = {
                type = "toggle",
                name = L["Auto Apply Changes"] or "Auto Apply Changes",
                desc = L["Automatically apply settings when you change them"] or "Automatically apply settings when you change them",
                order = 3,
                get = get,
                set = set,
            },
            unifyCameraDistance = {
                type = "toggle",
                name = L["Unify Camera Distance"] or "Unify Camera Distance",
                desc = L["Set the same camera distance for all characters (recommended for consistent experience)"] or "Set the same camera distance for all characters (recommended for consistent experience)",
                order = 4,
                get = get,
                set = set,
            },
            autoDetectDistance = {
                type = "toggle",
                name = L["Auto-Detect Optimal Distance"] or "Auto-Detect Optimal Distance",
                desc = L["Automatically detect optimal camera distance based on character's current camera position"] or "Automatically detect optimal camera distance based on character's current camera position",
                order = 5,
                get = get,
                set = set,
                disabled = function() return MaxCamEnhanced.db.profile.unifyCameraDistance end,
            },
            spacer1 = {
                type = "header",
                name = L["Camera Distance Settings"] or "Camera Distance Settings",
                order = 6,
            },
            cameraDistance = {
                type = "range",
                name = L["Camera Distance"] or "Camera Distance",
                desc = L["Camera distance - this will be both your default position AND maximum zoom distance"] or "Camera distance - this will be both your default position AND maximum zoom distance",
                min = 1,
                max = 100,
                step = 1,
                bigStep = 5,
                order = 7,
                get = get,
                set = function(info, value)
                    -- Set both camera distance and unified distance to the same value
                    MaxCamEnhanced.db.profile.cameraDistance = value
                    MaxCamEnhanced.db.profile.unifiedSavedDistance = value

                    -- Apply settings immediately if auto-apply is enabled, but with throttling
                    if MaxCamEnhanced.db.profile.autoApply then
                        -- Throttle rapid changes
                        if MaxCamEnhanced.applyTimer then
                            MaxCamEnhanced.applyTimer:Cancel()
                        end
                        MaxCamEnhanced.applyTimer = C_Timer.NewTimer(0.5, function()
                            MaxCamEnhanced:ApplySettings()
                            MaxCamEnhanced.applyTimer = nil
                            -- Обновляем отображение текущих значений WTF после применения
                            MaxCamEnhanced:UpdateCurrentValues()
                        end)
                    else
                        -- Если автоприменение отключено, все равно обновляем отображение
                        MaxCamEnhanced:UpdateCurrentValues()
                    end
                end,
                disabled = function() return not MaxCamEnhanced.db.profile.enabled or MaxCamEnhanced.db.profile.autoDetectDistance end,
            },
            applyOnZoneChange = {
                type = "toggle",
                name = L["Apply on Zone Change"] or "Apply on Zone Change",
                desc = L["Automatically apply camera distance when entering new zones"] or "Automatically apply camera distance when entering new zones",
                order = 9,
                get = get,
                set = set,
                disabled = function() return not MaxCamEnhanced.db.profile.enabled end,
            },

            spacer2 = {
                type = "header",
                name = L["Actions"] or "Actions",
                order = 10,
            },

            applyNow = {
                type = "execute",
                name = L["Apply Settings Now"] or "Apply Settings Now",
                desc = L["Apply current camera distance settings immediately"] or "Apply current camera distance settings immediately",
                order = 10.1,
                func = function()
                    MaxCamEnhanced:ApplySettings()
                    MaxCamEnhanced:UpdateCurrentValues()
                end,
                disabled = function() return not MaxCamEnhanced.db.profile.enabled end,
            },
            resetDefaults = {
                type = "execute",
                name = L["Reset to Defaults"] or "Reset to Defaults",
                desc = L["Reset all settings to default values (distance: 30, enabled, unified)"] or "Reset all settings to default values",
                order = 10.2,
                func = function()
                    MaxCamEnhanced.db:ResetProfile()
                    MaxCamEnhanced:ApplySettings()
                    MaxCamEnhanced:UpdateCurrentValues()
                end,
            },

            spacer3 = {
                type = "header",
                name = L["Current WTF Values:"] or "Current WTF Values:",
                order = 11,
            },
            currentValues = {
                type = "description",
                name = function()
                    local currentSaved = GetCVar("cameraSavedDistance") or "Unknown"
                    local currentMax = GetCVar("cameraDistanceMax") or "Unknown"
                    local currentPitch = GetCVar("cameraSavedPitch") or "Unknown"

                    local text = "|cFFFFFF00" .. (L["Current WTF Values:"] or "Current WTF Values:") .. "|r\n"
                    text = text .. "• " .. (L["Camera Saved Distance:"] or "Camera Saved Distance:") .. " |cFF00FF00" .. currentSaved .. "|r (where camera sits)\n"
                    text = text .. "• " .. (L["Camera Max Distance:"] or "Camera Max Distance:") .. " |cFF00FF00" .. currentMax .. "|r (zoom out limit)\n"
                    text = text .. "• " .. (L["Camera Pitch:"] or "Camera Pitch:") .. " |cFF888888" .. currentPitch .. "|r " .. (L["(read-only, not controlled)"] or "(read-only, not controlled)") .. "\n\n"

                    if MaxCamEnhanced.db.profile.unifyCameraDistance then
                        local distance = MaxCamEnhanced.db.profile.cameraDistance

                        text = text .. "|cFF88FF88" .. (L["Camera Distance Setting:"] or "Camera Distance Setting:") .. " " .. distance .. "|r " .. (L["(both position and zoom limit)"] or "(both position and zoom limit)") .. "\n"
                        text = text .. "|cFF88FF88" .. (L["Settings look correct"] or "Settings look correct") .. "|r\n"
                        text = text .. "|cFF888888" .. (L["(Values are automatically unified on login)"] or "(Values are automatically unified on login)") .. "|r"
                    else
                        text = text .. "|cFF888888" .. (L["(Enable unification to standardize values)"] or "(Enable unification to standardize values)") .. "|r"
                    end

                    return text
                end,
                order = 12,
            },
            spacer4 = {
                type = "header",
                name = L["Actions"] or "Actions",
                order = 13,
            },
            apply = {
                type = "execute",
                name = L["Apply Settings Now"] or "Apply Settings Now",
                desc = L["Apply current camera distance settings immediately"] or "Apply current camera distance settings immediately",
                order = 14,
                func = function()
                    MaxCamEnhanced:ApplySettings()
                    -- Show message only when manually applied
                    if MaxCamEnhanced.db.profile.enabled then
                        local distance = MaxCamEnhanced.db.profile.cameraDistance
                        if MaxCamEnhanced.db.profile.unifyCameraDistance then
                            print("|cFF00FF00MaxCamEnhanced:|r Camera distance set to " .. distance .. " (both default position and max zoom)")
                        else
                            print("|cFF00FF00MaxCamEnhanced:|r Camera distance applied: " .. distance)
                        end
                    else
                        print("|cFF00FF00MaxCamEnhanced:|r Camera distance disabled")
                    end
                end,
            },
            reset = {
                type = "execute",
                name = L["Reset to Defaults"] or "Reset to Defaults",
                desc = L["Reset all settings to default values (distance: 30, enabled, unified)"] or "Reset all settings to default values (distance: 30, enabled, unified)",
                order = 15,
                func = function()
                    MaxCamEnhanced:ResetToDefaults()
                    print("|cFF00FF00MaxCamEnhanced:|r " .. (L["Reset to defaults"] or "Reset to defaults"))
                end,
            },
        }
    }

    return options
end

-- Configuration initialization function (called after addon load)
function MaxCamEnhanced.InitializeConfig()
    if MaxCamEnhanced.configInitialized then
        return -- Prevent duplicate registration
    end

    -- Ensure database is initialized
    if not MaxCamEnhanced or not MaxCamEnhanced.db then
        print("MaxCamEnhanced: Database not ready, retrying in 1 second...")
        C_Timer.After(1, function()
            MaxCamEnhanced.InitializeConfig()
        end)
        return
    end

    local options = createOptions()
    -- Unregister any existing registration first
    pcall(function() AceConfigDialog:Close("MaxCamEnhanced") end)

    AceConfig:RegisterOptionsTable("MaxCamEnhanced", options)
    AceConfigDialog:AddToBlizOptions("MaxCamEnhanced", "MaxCamEnhanced")

    -- Hook interface close to apply settings
    local originalHide = InterfaceOptionsFrame.Hide
    InterfaceOptionsFrame.Hide = function(self)
        -- Apply settings when interface is closed
        if MaxCamEnhanced and MaxCamEnhanced.ApplySettings then
            MaxCamEnhanced:ApplySettings()
        end
        originalHide(self)
    end

    MaxCamEnhanced.configInitialized = true
end
