
SkadaDB = {
	["namespaces"] = {
		["LibDualSpec-1.0"] = {
		},
	},
	["profileKeys"] = {
		["Anelny - Icecrown"] = "My",
	},
	["global"] = {
		["spellstrings"] = {
			[48111] = "48111.2",
			[43017] = "43017.64",
			[64495] = "64495.1",
			[71184] = "71184.8",
			[67671] = "67671.64",
			[53365] = "53365.1",
			[42650] = "42650.32",
			[53908] = "53908.1",
			[23161] = "23161.1",
			[65182] = "65182.1",
			[71568] = "71568.1",
			[71600] = "71600.1",
			[48942] = "48942.2",
			[48447] = "48447.8",
			[3045] = "3045.1",
			[14203] = "14203.1",
			[74507] = "74507.64",
			[47585] = "47585.1",
			[1784] = "1784.1",
			[71601] = "71601.1",
			[57358] = "57358.1",
			[33357] = "33357.1",
			[57933] = "57933.1",
			[75466] = "75466.1",
			[53909] = "53909.1",
			[768] = "768.1",
			[59578] = "59578.1",
			[20375] = "20375.2",
			[59626] = "59626.32",
			[47889] = "47889.32",
			[48943] = "48943.2",
			[1539] = "1539.1",
			[2565] = "2565.1",
			[70772] = "70772.2",
			[16870] = "16870.8",
			[16886] = "16886.8",
			[29166] = "29166.8",
			[51690] = "51690.1",
			[57327] = "57327.1",
			[71635] = "71635.1",
			[11305] = "11305.1",
			[18499] = "18499.1",
			[71220] = "71220.8",
			[31801] = "31801.2",
			[57934] = "57934.1",
			[1038] = "1038.2",
			[63619] = "63619.64",
			[59547] = "59547.2",
			[67388] = "67388.8",
			[130] = "130.64",
			[71572] = "71572.1",
			[35099] = "35099.1",
			[71636] = "71636.64",
			[46916] = "46916.1",
			[15473] = "15473.32",
			[70806] = "70806.1",
			[48018] = "48018.32",
			[69369] = "69369.1",
			[61847] = "61847.8",
			[48066] = "48066.2",
			[71541] = "71541.64",
			[43020] = "43020.64",
			[48162] = "48162.2",
			[12970] = "12970.1",
			[5225] = "5225.1",
			[34477] = "34477.1",
			[50334] = "50334.1",
			[67358] = "67358.8",
			[43771] = "43771.1",
			[59628] = "59628.1",
			[47891] = "47891.32",
			[48418] = "48418.8",
			[48945] = "48945.2",
			[54646] = "54646.64",
			[64371] = "64371.32",
			[66] = "66.64",
			[17941] = "17941.32",
			[45544] = "45544.1",
			[54758] = "54758.1",
			[57329] = "57329.1",
			[19506] = "19506.64",
			[2645] = "2645.8",
			[70809] = "70809.8",
			[31834] = "31834.2",
			[31842] = "31842.2",
			[36554] = "36554.1",
			[32409] = "32409.32",
			[48451] = "48451.8",
			[48467] = "48467.8",
			[55637] = "55637.2",
			[67744] = "67744.1",
			[14204] = "14204.1",
			[75473] = "75473.1",
			[20217] = "20217.2",
			[48068] = "48068.2",
			[45529] = "45529.1",
			[71577] = "71577.1",
			[15258] = "15258.32",
			[14751] = "14751.1",
			[48707] = "48707.32",
			[70747] = "70747.32",
			[59545] = "59545.2",
			[15286] = "15286.32",
			[12051] = "12051.64",
			[14183] = "14183.1",
			[31665] = "31665.1",
			[45242] = "45242.2",
			[53434] = "53434.1",
			[27012] = "27012.8",
			[32223] = "32223.2",
			[71227] = "71227.8",
			[59752] = "59752.1",
			[47893] = "47893.32",
			[64772] = "64772.1",
			[48947] = "48947.2",
			[3714] = "3714.1",
			[48420] = "48420.8",
			[33151] = "33151.2",
			[19753] = "19753.2",
			[54648] = "54648.64",
			[12292] = "12292.1",
			[61834] = "61834.64",
			[70940] = "70940.1",
			[67708] = "67708.2",
			[8220] = "8220.1",
			[56161] = "56161.2",
			[54216] = "54216.1",
			[63734] = "63734.2",
			[71643] = "71643.1",
			[26669] = "26669.1",
			[32851] = "32851.1",
			[53307] = "53307.8",
			[20911] = "20911.2",
			[13877] = "13877.1",
			[72588] = "72588.8",
			[70845] = "70845.1",
			[67743] = "67743.1",
			[53403] = "53403.1",
			[73650] = "73650.2",
			[48161] = "48161.2",
			[11350] = "11350.4",
			[71548] = "71548.2",
			[53563] = "53563.2",
			[16593] = "16593.1",
			[71644] = "71644.1",
			[71165] = "71165.1",
			[71197] = "71197.8",
			[71229] = "71229.8",
			[67684] = "67684.1",
			[48659] = "48659.1",
			[75477] = "75477.64",
			[48517] = "48517.1",
			[61819] = "61819.1",
			[64901] = "64901.2",
			[27827] = "27827.2",
			[71485] = "71485.1",
			[70654] = "70654.32",
			[5215] = "5215.1",
			[43024] = "43024.64",
			[53755] = "53755.1",
			[28880] = "28880.2",
			[57356] = "57356.1",
			[50589] = "50589.4",
			[54153] = "54153.2",
			[58434] = "58434.64",
			[58450] = "58450.8",
			[55775] = "55775.1",
			[49284] = "49284.8",
			[72412] = "72412.1",
			[55173] = "55173.1",
			[70840] = "70840.32",
			[71486] = "71486.1",
			[71007] = "71007.8",
			[55001] = "55001.1",
			[71187] = "71187.8",
			[19883] = "19883.1",
			[70995] = "70995.1",
			[48422] = "48422.8",
			[61316] = "61316.64",
			[71638] = "71638.1",
			[48470] = "48470.8",
			[12536] = "12536.64",
			[63321] = "63321.4",
			[48518] = "48518.1",
			[10060] = "10060.2",
			[58882] = "58882.1",
			[17116] = "17116.1",
			[54203] = "54203.2",
			[33779] = "33779.1",
			[63944] = "63944.2",
			[70657] = "70657.1",
			[67016] = "67016.1",
			[65014] = "65014.1",
			[28714] = "28714.1",
			[50227] = "50227.1",
			[70721] = "70721.32",
			[70753] = "70753.32",
			[6940] = "6940.2",
			[75480] = "75480.64",
			[48263] = "48263.16",
			[72414] = "72414.1",
			[69910] = "69910.2",
			[19746] = "19746.2",
			[13159] = "13159.8",
			[35079] = "35079.1",
			[17057] = "17057.1",
			[71584] = "71584.1",
			[48391] = "48391.1",
			[55503] = "55503.8",
			[17] = "17.2",
			[552] = "552.2",
			[73422] = "73422.1",
			[16237] = "16237.2",
			[46947] = "46947.1",
			[2458] = "2458.1",
			[2457] = "2457.1",
			[58578] = "58578.1",
			[71] = "71.1",
			[16188] = "16188.1",
			[43010] = "43010.4",
			[66922] = "66922.2",
			[51713] = "51713.1",
			[9634] = "9634.1",
			[64999] = "64999.1",
			[28507] = "28507.1",
			[48168] = "48168.2",
			[15271] = "15271.32",
			[49222] = "49222.8",
			[72416] = "72416.64",
			[13750] = "13750.1",
			[45182] = "45182.1",
			[47753] = "47753.2",
			[71905] = "71905.1",
			[54428] = "54428.2",
			[25780] = "25780.2",
			[53762] = "53762.1",
			[71866] = "71866.2",
			[35706] = "35706.32",
			[71586] = "71586.2",
			[19884] = "19884.1",
			[35696] = "35696.1",
			[5118] = "5118.8",
			[70691] = "70691.8",
			[48990] = "48990.8",
			[6150] = "6150.1",
			[71403] = "71403.1",
			[45438] = "45438.16",
			[69926] = "69926.8",
			[33206] = "33206.2",
			[64904] = "64904.2",
			[67371] = "67371.8",
			[71491] = "71491.1",
			[48421] = "48421.8",
			[33891] = "33891.1",
			[67773] = "67773.2",
			[53758] = "53758.1",
			[48169] = "48169.32",
			[50213] = "50213.1",
			[61024] = "61024.64",
			[57399] = "57399.1",
			[11319] = "11319.1",
			[48792] = "48792.1",
			[31821] = "31821.1",
			[48265] = "48265.32",
			[72418] = "72418.64",
			[20132] = "20132.1",
			[47930] = "47930.2",
			[71492] = "71492.1",
			[20236] = "20236.2",
			[71556] = "71556.1",
			[58597] = "58597.2",
			[53200] = "53200.64",
			[23214] = "23214.2",
			[586] = "586.32",
			[48952] = "48952.2",
			[16595] = "16595.1",
			[73313] = "73313.32",
			[23885] = "23885.1",
			[49016] = "49016.1",
			[55164] = "55164.1",
			[58374] = "58374.1",
			[67378] = "67378.8",
			[51124] = "51124.1",
			[48074] = "48074.2",
			[48090] = "48090.1",
			[24907] = "24907.8",
			[43012] = "43012.16",
			[59891] = "59891.2",
			[71142] = "71142.8",
			[48170] = "48170.2",
			[66803] = "66803.32",
			[53312] = "53312.8",
			[54861] = "54861.1",
			[75458] = "75458.64",
			[42940] = "42940.16",
			[48266] = "48266.1",
			[6774] = "6774.1",
			[32612] = "32612.64",
			[70855] = "70855.8",
			[22812] = "22812.8",
			[28093] = "28093.1",
			[71558] = "71558.1",
			[59620] = "59620.2",
			[47883] = "47883.32",
			[1953] = "1953.64",
			[71175] = "71175.8",
			[2379] = "2379.1",
			[70728] = "70728.32",
			[70760] = "70760.2",
			[29131] = "29131.1",
			[61792] = "61792.1",
			[52610] = "52610.1",
			[48156] = "48156.32",
			[69898] = "69898.8",
			[64411] = "64411.1",
			[2893] = "2893.8",
			[53201] = "53201.64",
			[71559] = "71559.1",
			[48020] = "48020.32",
			[53760] = "53760.1",
			[71561] = "71561.1",
			[1706] = "1706.2",
			[10278] = "10278.2",
			[3411] = "3411.1",
			[982] = "982.8",
			[48778] = "48778.32",
			[57960] = "57960.8",
			[75456] = "75456.64",
			[71177] = "71177.8",
			[47788] = "47788.2",
			[48443] = "48443.8",
			[47820] = "47820.4",
			[71401] = "71401.64",
			[71560] = "71560.1",
			[53601] = "53601.2",
			[63725] = "63725.2",
			[64859] = "64859.1",
			[48938] = "48938.2",
			[20165] = "20165.2",
			[13168] = "13168.1",
			[73828] = "73828.1",
			[64843] = "64843.2",
			[14202] = "14202.1",
			[55166] = "55166.1",
			[6346] = "6346.2",
			[55198] = "55198.1",
			[71432] = "71432.1",
			[1719] = "1719.1",
			[20053] = "20053.1",
			[48108] = "48108.1",
			[25898] = "25898.2",
			[67695] = "67695.64",
			[43046] = "43046.4",
			[19263] = "19263.1",
			[55342] = "55342.64",
			[1044] = "1044.2",
			[65000] = "65000.64",
			[48504] = "48504.8",
			[64713] = "64713.1",
			[24932] = "24932.1",
			[75490] = "75490.1",
			[59542] = "59542.2",
			[36563] = "36563.1",
			[69705] = "69705.32",
			[63167] = "63167.1",
			[64205] = "64205.1",
			[48441] = "48441.8",
			[57623] = "57623.1",
			[50421] = "50421.1",
			[71396] = "71396.1",
			[50449] = "50449.1",
			[32182] = "32182.8",
			[60229] = "60229.2",
			[64844] = "64844.2",
			[62305] = "62305.1",
			[15359] = "15359.2",
			[55694] = "55694.1",
			[72968] = "72968.1",
			[64413] = "64413.8",
			[31224] = "31224.1",
			[498] = "498.2",
			[54508] = "54508.1",
			[43015] = "43015.64",
			[53251] = "53251.8",
			[54043] = "54043.2",
			[57371] = "57371.1",
			[53390] = "53390.1",
			[51271] = "51271.1",
			[24858] = "24858.1",
			[64440] = "64440.1",
			[58984] = "58984.1",
			[59000] = "59000.32",
			[70893] = "70893.1",
			[1742] = "1742.1",
			[67380] = "67380.8",
			[61619] = "61619.1",
			[25228] = "25228.32",
			[71564] = "71564.1",
			[48934] = "48934.2",
			[31884] = "31884.2",
			[52000] = "52000.8",
			[47241] = "47241.1",
			[20166] = "20166.2",
			[73174] = "73174.40",
			[1002] = "1002.8",
			[28682] = "28682.4",
			[69808] = "69808.1",
			[67750] = "67750.1",
			[75493] = "75493.2",
			[57567] = "57567.32",
			[20230] = "20230.1",
			[53401] = "53401.1",
			[43002] = "43002.64",
			[53220] = "53220.1",
			[20765] = "20765.32",
			[642] = "642.2",
			[25899] = "25899.2",
			[18708] = "18708.32",
			[71605] = "71605.64",
			[67669] = "67669.64",
			[57073] = "57073.1",
			[54149] = "54149.2",
			[34123] = "34123.1",
			[48469] = "48469.8",
			[75494] = "75494.2",
			[48932] = "48932.2",
			[57516] = "57516.1",
			[34026] = "34026.1",
			[69882] = "69882.8",
			[47855] = "47855.32",
			[34074] = "34074.8",
			[61684] = "61684.1",
			[73173] = "73173.40",
			[5502] = "5502.2",
			[20216] = "20216.2",
			[47440] = "47440.1",
			[783] = "783.1",
			[16591] = "16591.1",
			[54131] = "54131.1",
			[61301] = "61301.8",
			[75495] = "75495.1",
			[59052] = "59052.16",
			[67383] = "67383.8",
			[55233] = "55233.1",
			[25661] = "25661.1",
		},
		["nicknames"] = {
			["cache"] = {
			},
			["reset"] = 1753335297,
		},
	},
	["profiles"] = {
		["Default"] = {
			["showtotals"] = true,
			["scroll"] = {
				["speed"] = 2,
				["icon"] = true,
				["button"] = "MiddleButton",
			},
			["namedisplay"] = 2,
			["modules"] = {
				["deathlogevents"] = 14,
				["failschannel"] = "AUTO",
				["sunderdelay"] = 20,
				["deathchannel"] = "AUTO",
				["parrychannel"] = "AUTO",
				["sunderchannel"] = "SAY",
				["interruptchannel"] = "SAY",
				["alternativedeaths"] = true,
				["threat"] = {
					["soundfile"] = "Humm",
					["ignorePets"] = true,
					["flash"] = true,
					["message"] = true,
					["threshold"] = 90,
					["notankwarnings"] = true,
					["sound"] = true,
					["shake"] = true,
					["output"] = 1,
					["showAggroBar"] = true,
					["frequency"] = 2,
				},
				["deathlogthreshold"] = 1000,
				["killchannel"] = "SAY",
			},
			["prepotion"] = true,
			["windows"] = {
				{
					["barheight"] = 15,
					["buttons"] = {
						["menu"] = false,
						["mode"] = false,
					},
					["barwidth"] = 200,
					["reversegrowth"] = false,
					["title"] = {
						["height"] = 15,
						["swap"] = true,
					},
					["useselfcolor"] = false,
					["y"] = -262.7166642127046,
					["x"] = 658.518634189202,
					["barslocked"] = true,
					["showtotals"] = true,
					["moduleicons"] = true,
					["mode"] = "Damage",
					["background"] = {
						["height"] = 240,
						["texture"] = "None",
					},
					["classcolortext"] = true,
				}, -- [1]
			},
			["icon"] = {
				["hide"] = true,
			},
			["columns"] = {
				["Skada_Debuffs_Count"] = true,
				["Skada_Sunder Counter_Percent"] = true,
				["Skada_Sunder Counter_sPercent"] = true,
				["Skada_Buffs_Count"] = true,
			},
			["combatlogfixtime"] = 2,
			["usecustomcolors"] = true,
			["modulesBlocked"] = {
				["Killing Blows"] = false,
				["Healing Done By Spell"] = false,
				["CC Breaks"] = false,
				["Overhealing"] = false,
				["Useful Damage"] = false,
				["HPS"] = false,
				["My Spells"] = false,
				["DTPS"] = false,
				["Avoidance & Mitigation"] = false,
				["Enemy Healing Done"] = false,
				["Total Healing"] = false,
				["Damage Done By School"] = false,
				["Overkill"] = false,
				["Healthstones"] = false,
				["Enemy Debuffs"] = false,
				["Healing Taken"] = false,
				["Legacy Bar Display"] = false,
				["Absorbed Damage"] = false,
				["Casts"] = false,
				["Enemy Buffs"] = false,
				["Damage Done By Spell"] = false,
				["Data Text"] = false,
				["Improvement"] = false,
				["Inline Bar Display"] = false,
				["Player vs. Player"] = false,
			},
			["reportlinks"] = true,
			["totalidc"] = true,
			["sortmodesbyusage"] = true,
			["toast"] = {
				["hide_toasts"] = false,
			},
			["moduleicons"] = true,
			["spamage"] = true,
			["modeclicks"] = {
				["Deaths"] = 1,
				["Damage"] = 5,
			},
			["reset"] = {
				["join"] = 1,
			},
		},
		["My"] = {
			["scroll"] = {
				["speed"] = 2,
				["icon"] = true,
				["button"] = "MiddleButton",
			},
			["namedisplay"] = 2,
			["modules"] = {
				["deathlogevents"] = 14,
				["failschannel"] = "AUTO",
				["sunderdelay"] = 20,
				["deathchannel"] = "AUTO",
				["parrychannel"] = "AUTO",
				["sunderchannel"] = "SAY",
				["interruptchannel"] = "SAY",
				["alternativedeaths"] = true,
				["deathlogthreshold"] = 1000,
				["threat"] = {
					["soundfile"] = "Humm",
					["ignorePets"] = true,
					["flash"] = true,
					["threshold"] = 90,
					["output"] = 1,
					["notankwarnings"] = true,
					["showAggroBar"] = true,
					["shake"] = true,
					["message"] = true,
					["sound"] = true,
					["frequency"] = 2,
				},
				["killchannel"] = "SAY",
			},
			["prepotion"] = true,
			["windows"] = {
				{
					["barheight"] = 15,
					["useselfcolor"] = false,
					["barslocked"] = true,
					["set"] = 1,
					["y"] = -262.7166642127046,
					["x"] = 658.518634189202,
					["title"] = {
						["swap"] = true,
						["height"] = 15,
					},
					["background"] = {
						["height"] = 240,
						["texture"] = "None",
					},
					["showtotals"] = true,
					["reversegrowth"] = false,
					["classcolortext"] = true,
					["mode"] = "Damage",
					["buttons"] = {
						["menu"] = false,
						["mode"] = false,
					},
					["barwidth"] = 200,
					["moduleicons"] = true,
				}, -- [1]
			},
			["icon"] = {
				["hide"] = true,
			},
			["combatlogfixtime"] = 2,
			["sortmodesbyusage"] = true,
			["reset"] = {
				["join"] = 1,
			},
			["showtotals"] = true,
			["modulesBlocked"] = {
				["Killing Blows"] = false,
				["Healing Done By Spell"] = false,
				["CC Breaks"] = false,
				["Overhealing"] = false,
				["Useful Damage"] = false,
				["DTPS"] = false,
				["My Spells"] = false,
				["Player vs. Player"] = false,
				["Avoidance & Mitigation"] = false,
				["Enemy Healing Done"] = false,
				["Total Healing"] = false,
				["Damage Done By School"] = false,
				["Overkill"] = false,
				["Healthstones"] = false,
				["Improvement"] = false,
				["Healing Taken"] = false,
				["Legacy Bar Display"] = false,
				["Damage Done By Spell"] = false,
				["Casts"] = false,
				["Enemy Buffs"] = false,
				["Absorbed Damage"] = false,
				["Data Text"] = false,
				["HPS"] = false,
				["Inline Bar Display"] = false,
				["Enemy Debuffs"] = false,
			},
			["usecustomcolors"] = true,
			["toast"] = {
				["hide_toasts"] = false,
			},
			["modeclicks"] = {
				["Damage Taken"] = 1,
				["Deaths"] = 3,
				["Enemy Damage Taken"] = 5,
				["Damage"] = 11,
			},
			["columns"] = {
				["Skada_Debuffs_Count"] = true,
				["Skada_Buffs_Count"] = true,
				["Skada_Sunder Counter_sPercent"] = true,
				["Skada_Sunder Counter_Percent"] = true,
			},
			["spamage"] = true,
			["moduleicons"] = true,
			["totalidc"] = true,
			["reportlinks"] = true,
			["inCombat"] = false,
		},
	},
}
