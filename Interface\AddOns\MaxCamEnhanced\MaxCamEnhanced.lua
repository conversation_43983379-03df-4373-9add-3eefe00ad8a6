-- MaxCam Enhanced - Advanced camera distance control using Ace3
-- Version 2.0

-- Create addon using AceAddon-3.0
local MaxCamEnhanced = LibStub("AceAddon-3.0"):NewAddon("MaxCamEnhanced", "AceEvent-3.0", "AceConsole-3.0")
local L = MaxCamEnhanced_Locale or {}

-- Export to global scope
_G["MaxCamEnhanced"] = MaxCamEnhanced

-- Default settings
local defaults = {
    profile = {
        cameraDistance = 30,
        enabled = true,
        autoApply = true,
        applyOnZoneChange = true,
        autoDetectDistance = false,
        unifyCameraDistance = true,
        unifiedSavedDistance = 25
    }
}

function MaxCamEnhanced:OnInitialize()
    -- Initialize database
    self.db = LibStub("AceDB-3.0"):New("MaxCamEnhancedDB", defaults, "Default")

    -- Register slash commands
    self:RegisterChatCommand("maxcam", "SlashCmdHandler")
    self:RegisterChatCommand("mc", "SlashCmdHandler")

    print("|cFF00FF00MaxCamEnhanced v2.0 " .. (L["loaded! Type /maxcam config for settings."] or "loaded! Type /maxcam config for settings.") .. "|r")
end

function MaxCamEnhanced:OnEnable()
    -- Register events
    self:RegisterEvent("PLAYER_ENTERING_WORLD")
    self:RegisterEvent("ZONE_CHANGED_NEW_AREA")

    -- Initialize configuration with small delay
    C_Timer.After(0.1, function()
        if MaxCamEnhanced.InitializeConfig then
            MaxCamEnhanced.InitializeConfig()
        end
    end)
end

function MaxCamEnhanced:OnDisable()
    -- Clean up configuration registration
    self.configInitialized = false
end

-- Event handler - Apply settings once on login
function MaxCamEnhanced:PLAYER_ENTERING_WORLD()
    -- Apply camera settings on login (includes unification if enabled)
    self:ApplyCameraSettings()

    -- Unregister event after first application
    self:UnregisterEvent("PLAYER_ENTERING_WORLD")
end

-- Force unification for all characters by overriding WTF values (silent check)
function MaxCamEnhanced:ForceUnifyAllCharacters()
    if not self.db.profile.unifyCameraDistance then
        return
    end

    local unifiedDistance = self.db.profile.unifiedSavedDistance
    local maxDistance = math.max(unifiedDistance, self.db.profile.cameraDistance)

    -- Get current values to check if change is needed
    local currentSaved = GetCVar("cameraSavedDistance")
    local currentMax = GetCVar("cameraDistanceMax")
    local currentPitch = GetCVar("cameraSavedPitch")

    local needsUpdate = false

    -- Check if values need updating
    if not currentSaved or math.abs(tonumber(currentSaved) - unifiedDistance) > 0.1 then
        needsUpdate = true
    end
    if not currentMax or math.abs(tonumber(currentMax) - maxDistance) > 0.1 then
        needsUpdate = true
    end
    if not currentPitch or tonumber(currentPitch) ~= 20 then
        needsUpdate = true
    end

    if needsUpdate then
        -- Apply all values silently
        SetCVar("cameraSavedDistance", unifiedDistance)
        SetCVar("cameraDistanceMax", maxDistance)
        SetCVar("cameraSavedPitch", "20")

        -- Force to WTF files
        ConsoleExec("CameraDistanceMax " .. maxDistance)
        ConsoleExec("cvar_save")

        print("|cFF00FF00MaxCamEnhanced:|r Camera unified - Distance: " .. unifiedDistance .. ", Max: " .. maxDistance)
    end
end

function MaxCamEnhanced:ZONE_CHANGED_NEW_AREA()
    -- Apply camera distance when changing zones if enabled
    if self.db.profile.applyOnZoneChange then
        self:ApplyCameraSettings()
    end
end

-- Auto-detect optimal camera distance based on character's saved distance
function MaxCamEnhanced:AutoDetectOptimalDistance()
    local savedDistance = GetCVar("cameraSavedDistance")
    if not savedDistance then
        return 20 -- Default fallback
    end

    local currentDistance = tonumber(savedDistance)
    if not currentDistance then
        return 20 -- Default fallback
    end

    -- Calculate optimal max distance with buffer
    local optimalDistance
    if currentDistance <= 15 then
        -- Character prefers close camera, give moderate buffer
        optimalDistance = 25
    elseif currentDistance <= 20 then
        -- Character uses standard distance, give good buffer
        optimalDistance = 30
    elseif currentDistance <= 25 then
        -- Character prefers far camera, give larger buffer
        optimalDistance = 35
    else
        -- Character uses very far camera, give maximum buffer
        optimalDistance = math.min(currentDistance + 15, 100)
    end

    return math.floor(optimalDistance)
end

-- Apply camera settings with forced WTF modification
function MaxCamEnhanced:ApplyCameraSettings()
    if not self.db.profile.enabled then
        return
    end

    -- Prevent multiple rapid calls
    local now = GetTime()
    if self.lastApplyTime and (now - self.lastApplyTime) < 1 then
        return -- Skip if called within 1 second
    end
    self.lastApplyTime = now

    local distance = self.db.profile.cameraDistance

    -- Auto-detect optimal distance if enabled and unify is disabled
    if self.db.profile.autoDetectDistance and not self.db.profile.unifyCameraDistance then
        local autoDistance = self:AutoDetectOptimalDistance()
        if autoDistance ~= distance then
            distance = autoDistance
            self.db.profile.cameraDistance = distance
        end
    end

    -- Apply unification if enabled - FORCE to WTF files
    if self.db.profile.unifyCameraDistance then
        local unifiedDistance = self.db.profile.unifiedSavedDistance

        -- FORCE max distance to be MUCH larger than unified distance
        -- Unified distance is what camera will be set to, max distance is the limit
        local requiredMaxDistance = math.max(unifiedDistance * 2, unifiedDistance + 15)
        if distance < requiredMaxDistance then
            distance = requiredMaxDistance
            -- Update the setting so it persists
            self.db.profile.cameraDistance = distance
            print("|cFF00FF00MaxCamEnhanced:|r Auto-corrected max distance from " .. self.db.profile.cameraDistance .. " to " .. distance)
        end

        -- Apply all settings with forced WTF save
        SetCVar("cameraDistanceMax", distance)
        SetCVar("cameraSavedDistance", unifiedDistance)
        SetCVar("cameraSavedPitch", "20")

        -- Force to WTF files using ConsoleExec
        ConsoleExec("CameraDistanceMax " .. distance)

        -- Force save all CVars to WTF files
        ConsoleExec("cvar_save")

        print("|cFF00FF00MaxCamEnhanced:|r Camera unified - Distance: " .. unifiedDistance .. ", Max: " .. distance .. " (saved to WTF)")
    else
        -- Just apply max distance if unification is disabled
        SetCVar("cameraDistanceMax", distance)
        ConsoleExec("CameraDistanceMax " .. distance)
    end
end

-- Force save current CVars to WTF files using multiple methods
function MaxCamEnhanced:ForceWTFSave()
    -- Method 1: Use ConsoleExec for camera distance (most reliable)
    local maxDist = GetCVar("cameraDistanceMax")
    if maxDist then
        ConsoleExec("CameraDistanceMax " .. maxDist)
    end

    -- Method 2: Force save all CVars
    ConsoleExec("cvar_save")

    -- Method 3: Set CVars with forced persistence flag
    local savedDist = GetCVar("cameraSavedDistance")
    local savedPitch = GetCVar("cameraSavedPitch")
    if savedDist then
        SetCVar("cameraSavedDistance", savedDist, "1")  -- Force save flag
    end
    if savedPitch then
        SetCVar("cameraSavedPitch", savedPitch, "1")  -- Force save flag
    end

    -- Method 4: Try alternative console commands
    if maxDist then
        ConsoleExec("cameraDistanceMaxFactor " .. (tonumber(maxDist) / 15))  -- Factor relative to default
    end

    -- Method 5: Force reload of camera settings
    ConsoleExec("cvar_reload")
end

-- Apply settings to game (simplified like MouseSpeedEnhanced)
function MaxCamEnhanced:ApplySettings()
    if self.db.profile.enabled then
        self:ApplyCameraSettings()
    else
        -- Reset to default WoW camera distance using SetCVar
        SetCVar("cameraDistanceMax", "15")
    end

    -- Settings apply silently like MouseSpeedEnhanced
end

-- Reset to defaults (like MouseSpeedEnhanced)
function MaxCamEnhanced:ResetToDefaults()
    self.db:ResetProfile()
    self:ApplySettings()
    -- Silent reset like MouseSpeedEnhanced
end

-- Slash command handler
function MaxCamEnhanced:SlashCmdHandler(input)
    local command = string.lower(input or "")

    if command == "config" or command == "settings" or command == "" then
        -- Force initialize config if not ready
        if not MaxCamEnhanced.configInitialized then
            print("MaxCamEnhanced: Initializing configuration...")
            MaxCamEnhanced.InitializeConfig()
        end
        -- Open Blizzard interface options to our addon
        InterfaceOptionsFrame_OpenToCategory("MaxCamEnhanced")
        InterfaceOptionsFrame_OpenToCategory("MaxCamEnhanced") -- Call twice for proper focus
    elseif command == "apply" then
        self:ApplySettings()
    elseif command == "reset" then
        self:ResetToDefaults()
    elseif command == "toggle" then
        self.db.profile.enabled = not self.db.profile.enabled
        self:ApplySettings()
        if self.db.profile.enabled then
            print("|cFF00FF00MaxCamEnhanced:|r " .. (L["Camera distance applied:"] or "Camera distance applied:") .. " " .. self.db.profile.cameraDistance)
        else
            print("|cFF00FF00MaxCamEnhanced:|r " .. (L["Camera distance disabled"] or "Camera distance disabled"))
        end
    elseif string.match(command, "^set%s+(%d+)$") then
        local distance = tonumber(string.match(command, "^set%s+(%d+)$"))
        if distance and distance >= 1 and distance <= 100 then
            self.db.profile.cameraDistance = distance
            self:ApplySettings()
            print("|cFF00FF00MaxCamEnhanced:|r Camera distance set to " .. distance)
        else
            print("|cFFFF0000MaxCamEnhanced:|r Invalid distance. Use values between 1 and 100.")
        end
    elseif command == "info" or command == "debug" then
        print("|cFF00FF00MaxCamEnhanced Character Info:|r")
        print("Character: " .. UnitName("player"))
        print("Race: " .. UnitRace("player"))
        print("Current Setting: " .. (self.db.profile.cameraDistance or "Not set"))
        print("Enabled: " .. (self.db.profile.enabled and "Yes" or "No"))
        print("Unify Enabled: " .. (self.db.profile.unifyCameraDistance and "Yes" or "No"))
        if self.db.profile.unifyCameraDistance then
            print("Unify Target: " .. (self.db.profile.unifiedSavedDistance or "Not set"))
        end
        print("Auto-Detect: " .. (self.db.profile.autoDetectDistance and "Yes" or "No"))
        print("|cFFFFFF00Current CVars:|r")
        print("  cameraDistanceMax: " .. (GetCVar("cameraDistanceMax") or "Unknown"))
        print("  cameraSavedDistance: " .. (GetCVar("cameraSavedDistance") or "Unknown"))
        print("  cameraSavedPitch: " .. (GetCVar("cameraSavedPitch") or "Unknown"))

        -- Test if camera actually zooms out to max distance
        print("|cFFFFFF00Camera Test:|r")
        print("Try zooming out with mouse wheel - does it go beyond 15 units?")
        print("If not, the WTF values may not be applied correctly.")
        print("Default WoW Distance: 15")
        local optimal = self:AutoDetectOptimalDistance()
        print("Recommended Distance: " .. optimal)
    elseif command == "force" then
        print("|cFF00FF00MaxCamEnhanced:|r Forcing camera settings...")

        -- Force correct max distance if unification is enabled
        if self.db.profile.unifyCameraDistance then
            local unifiedDistance = self.db.profile.unifiedSavedDistance
            local requiredMaxDistance = math.max(unifiedDistance * 2, unifiedDistance + 15)

            if self.db.profile.cameraDistance < requiredMaxDistance then
                local oldMax = self.db.profile.cameraDistance
                self.db.profile.cameraDistance = requiredMaxDistance
                print("|cFF00FF00MaxCamEnhanced:|r Corrected max distance from " .. oldMax .. " to " .. requiredMaxDistance)
            end
        end

        -- Reset throttling and force apply
        self.lastApplyTime = nil
        self:ApplyCameraSettings()
        self:ForceWTFSave()
        print("|cFF00FF00MaxCamEnhanced:|r Settings forced to WTF files!")
    elseif command == "fix" then
        print("|cFF00FF00MaxCamEnhanced:|r Checking and fixing settings...")

        if self.db.profile.unifyCameraDistance then
            local unifiedDistance = self.db.profile.unifiedSavedDistance
            local currentMaxDistance = self.db.profile.cameraDistance
            local requiredMaxDistance = math.max(unifiedDistance * 2, unifiedDistance + 15)

            print("Unified Distance (camera position): " .. unifiedDistance)
            print("Current Max Distance (zoom limit): " .. currentMaxDistance)
            print("Required Max Distance (should be): " .. requiredMaxDistance)
            print("")
            print("|cFFFFFF00Explanation:|r")
            print("• Unified Distance = where camera sits by default")
            print("• Max Distance = how far you can zoom out")
            print("• Max should be MUCH larger than Unified for proper zooming")
            print("")

            if currentMaxDistance < requiredMaxDistance then
                self.db.profile.cameraDistance = requiredMaxDistance
                print("|cFF00FF00FIXED:|r Max distance changed from " .. currentMaxDistance .. " to " .. requiredMaxDistance)
            else
                print("|cFF00FF00OK:|r Max distance is sufficient")
            end

            -- Apply the corrected settings
            self.lastApplyTime = nil
            self:ApplyCameraSettings()
        else
            print("|cFF888888Unification is disabled - no fixes needed|r")
        end
    elseif command == "test" then
        print("|cFF00FF00MaxCamEnhanced:|r Testing camera distance...")

        -- Get current values
        local maxDist = GetCVar("cameraDistanceMax")
        local savedDist = GetCVar("cameraSavedDistance")

        print("Current cameraDistanceMax: " .. (maxDist or "Unknown"))
        print("Current cameraSavedDistance: " .. (savedDist or "Unknown"))

        -- Try to set camera to max distance
        if maxDist then
            local testDistance = tonumber(maxDist)
            if testDistance then
                -- Force camera to zoom out to max distance
                ConsoleExec("CameraDistanceMax " .. testDistance)
                SetCVar("cameraDistanceMax", testDistance)

                -- Try to set current camera distance using WoW 3.3.5a methods
                CameraZoomOut(testDistance)  -- Built-in function

                print("Attempted to set camera distance to " .. testDistance)
                print("Try zooming with mouse wheel - does it go to " .. testDistance .. "?")
            end
        end
    elseif string.match(command, "^zoom") then
        local distance = tonumber(string.match(command, "^zoom%s+(%d+)$"))
        if not distance then
            distance = 25  -- Default test distance
        end

        print("|cFF00FF00MaxCamEnhanced:|r Testing zoom to distance " .. distance)

        -- Force set max distance
        SetCVar("cameraDistanceMax", distance)
        ConsoleExec("CameraDistanceMax " .. distance)

        -- Try to zoom camera out using WoW 3.3.5a methods
        for i = 1, distance do
            CameraZoomOut(1)  -- Zoom out step by step
        end

        -- Alternative method
        ConsoleExec("cameraDistanceMaxFactor " .. (distance / 15))

        print("Camera should now be at distance " .. distance)
        print("Current cameraDistanceMax: " .. (GetCVar("cameraDistanceMax") or "Unknown"))

        -- Force save
        self:ForceWTFSave()
    elseif command == "reload" then
        print("|cFF00FF00MaxCamEnhanced:|r Reloading addon...")
        MaxCamEnhanced.configInitialized = false
        MaxCamEnhanced.InitializeConfig()
        MaxCamEnhanced:ApplySettings()
    elseif command == "help" then
        print("|cFF00FF00MaxCamEnhanced " .. (L["Commands:"] or "Commands:") .. "|r")
        print("|cFFFFFF00/maxcam|r or |cFFFFFF00/mc|r - " .. (L["Open settings"] or "Open settings"))
        print("|cFFFFFF00/maxcam apply|r - " .. (L["Apply current settings"] or "Apply current settings"))
        print("|cFFFFFF00/maxcam toggle|r - Toggle camera distance on/off")
        print("|cFFFFFF00/maxcam set <number>|r - Set distance (1-100), e.g. '/maxcam set 30'")
        print("|cFFFFFF00/maxcam info|r - Show character-specific camera info")
        print("|cFFFFFF00/maxcam reset|r - " .. (L["Reset to defaults"] or "Reset to defaults"))
        print("|cFFFFFF00/maxcam force|r - Force apply settings to WTF files")
        print("|cFFFFFF00/maxcam fix|r - Check and fix max distance settings")
        print("|cFFFFFF00/maxcam test|r - Test camera distance in real-time")
        print("|cFFFFFF00/maxcam zoom [distance]|r - Force zoom to specific distance (default: 25)")
        print("|cFFFFFF00/maxcam reload|r - Reload addon configuration")
        print("|cFFFFFF00/maxcam help|r - " .. (L["Show this help"] or "Show this help"))
        print("|cFF888888Note: Camera unification works automatically when enabled in settings.|r")
        print("|cFF888888Note: Unification and auto-detection work automatically when enabled in settings.|r")
    else
        print("|cFFFF0000MaxCamEnhanced:|r " .. (L["Unknown command. Type '/maxcam help' for available commands."] or "Unknown command. Type '/maxcam help' for available commands."))
    end
end
