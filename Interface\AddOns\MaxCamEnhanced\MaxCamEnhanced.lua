-- MaxCam Enhanced - Advanced camera distance control using Ace3
-- Version 2.0

-- Create addon using AceAddon-3.0
local MaxCamEnhanced = LibStub("AceAddon-3.0"):NewAddon("MaxCamEnhanced", "AceEvent-3.0", "AceConsole-3.0")
local L = MaxCamEnhanced_Locale or {}

-- Export to global scope
_G["MaxCamEnhanced"] = MaxCamEnhanced

-- Default settings
local defaults = {
    profile = {
        cameraDistance = 30,
        enabled = true,
        autoApply = true,
        applyOnZoneChange = true,
        autoDetectDistance = false,
        unifyCameraDistance = true,
        unifiedSavedDistance = 25,
        cameraPitch = 20,
        controlCameraPitch = false
    }
}

function MaxCamEnhanced:OnInitialize()
    -- Initialize database
    self.db = LibStub("AceDB-3.0"):New("MaxCamEnhancedDB", defaults, "Default")

    -- Check WoW version compatibility
    self:CheckCompatibility()

    -- Commands removed - use interface only
end

-- Check WoW version compatibility
function MaxCamEnhanced:CheckCompatibility()
    -- Test if camera pitch control works
    local pitchWorks = false
    local currentPitch = GetCVar("cameraSavedPitch")
    if currentPitch then
        local success = pcall(SetCVar, "cameraSavedPitch", currentPitch)
        pitchWorks = success
    end

    -- Disable pitch control if it doesn't work
    if not pitchWorks and self.db.profile.controlCameraPitch then
        self.db.profile.controlCameraPitch = false
    end

    -- Store compatibility info
    self.pitchSupported = pitchWorks
end

function MaxCamEnhanced:OnEnable()
    -- Register events
    self:RegisterEvent("PLAYER_ENTERING_WORLD")
    self:RegisterEvent("ZONE_CHANGED_NEW_AREA")
    self:RegisterEvent("ZONE_CHANGED")
    self:RegisterEvent("ZONE_CHANGED_INDOORS")

    -- Initialize configuration with small delay
    C_Timer.After(0.1, function()
        if MaxCamEnhanced.InitializeConfig then
            MaxCamEnhanced.InitializeConfig()
        end
    end)
end

function MaxCamEnhanced:OnDisable()
    -- Clean up configuration registration
    self.configInitialized = false
end

-- Event handler - Apply settings once on login
function MaxCamEnhanced:PLAYER_ENTERING_WORLD()
    -- Apply camera settings on login (includes unification if enabled)
    self:ApplyCameraSettings()

    -- Unregister event after first application
    self:UnregisterEvent("PLAYER_ENTERING_WORLD")
end

-- Force unification for all characters by overriding WTF values (silent check)
function MaxCamEnhanced:ForceUnifyAllCharacters()
    if not self.db.profile.unifyCameraDistance then
        return
    end

    local unifiedDistance = self.db.profile.unifiedSavedDistance
    local maxDistance = self.db.profile.cameraDistance

    -- Get current values to check if change is needed
    local currentSaved = GetCVar("cameraSavedDistance")
    local currentMax = GetCVar("cameraDistanceMax")
    local currentPitch = GetCVar("cameraSavedPitch")

    local needsUpdate = false

    -- Check if values need updating
    if not currentSaved or math.abs(tonumber(currentSaved) - unifiedDistance) > 0.1 then
        needsUpdate = true
    end
    if not currentMax or math.abs(tonumber(currentMax) - maxDistance) > 0.1 then
        needsUpdate = true
    end
    -- Don't modify pitch in 3.3.5a - it may not work properly

    if needsUpdate then
        -- Apply all values silently
        SetCVar("cameraSavedDistance", unifiedDistance)
        SetCVar("cameraDistanceMax", maxDistance)

        -- Apply camera pitch if control is enabled (may not work in 3.3.5a)
        if self.db.profile.controlCameraPitch then
            local success = pcall(SetCVar, "cameraSavedPitch", self.db.profile.cameraPitch)
            if not success then
                -- Pitch control not supported in this version
                self.db.profile.controlCameraPitch = false
            end
        end

        -- Force to WTF files
        ConsoleExec("CameraDistanceMax " .. maxDistance)
        ConsoleExec("CameraSavedDistance " .. unifiedDistance)
        ConsoleExec("cvar_save")

        -- Update config display
        self:UpdateCurrentValues()
    end
end

function MaxCamEnhanced:ZONE_CHANGED_NEW_AREA()
    -- Apply camera distance when changing zones if enabled
    if self.db.profile.applyOnZoneChange then
        self:ApplyCameraSettings()
    end

    -- Auto-detect for new location if enabled
    if self.db.profile.autoDetectDistance then
        self:ApplyCameraSettings()
    end
end

function MaxCamEnhanced:ZONE_CHANGED()
    -- Auto-detect for location change if enabled
    if self.db.profile.autoDetectDistance then
        self:ApplyCameraSettings()
    end
end

function MaxCamEnhanced:ZONE_CHANGED_INDOORS()
    -- Auto-detect for indoor/outdoor change if enabled
    if self.db.profile.autoDetectDistance then
        self:ApplyCameraSettings()
    end
end

-- Auto-detect optimal camera distance based on current location/environment
function MaxCamEnhanced:AutoDetectOptimalDistance()
    -- Get current location information
    local zone = GetZoneText() or ""
    local subzone = GetSubZoneText() or ""
    local isIndoors = IsIndoors()
    local isInInstance = IsInInstance()

    local optimalDistance = 30 -- Default fallback

    -- Detect location type and set appropriate camera distance
    if isInInstance then
        -- In dungeons/raids - usually need closer camera for better control
        optimalDistance = 25

        -- Specific instance adjustments
        if string.find(zone, "Naxxramas") or string.find(zone, "Icecrown Citadel") then
            optimalDistance = 30 -- Large raids need more distance
        elseif string.find(zone, "Deadmines") or string.find(zone, "Stockade") then
            optimalDistance = 20 -- Small dungeons need less distance
        end

    elseif isIndoors then
        -- Indoor locations (buildings, caves) - closer camera
        optimalDistance = 20

        -- Specific indoor adjustments
        if string.find(zone, "Stormwind") or string.find(zone, "Orgrimmar") then
            optimalDistance = 25 -- Cities can handle medium distance
        elseif string.find(subzone, "Inn") or string.find(subzone, "Bank") then
            optimalDistance = 15 -- Very close spaces
        end

    else
        -- Outdoor locations - farther camera for better view
        optimalDistance = 40

        -- Specific outdoor adjustments
        if string.find(zone, "Wintergrasp") or string.find(zone, "Alterac Valley") then
            optimalDistance = 50 -- PvP areas need maximum view
        elseif string.find(zone, "Dalaran") or string.find(zone, "Shattrath") then
            optimalDistance = 30 -- Neutral cities
        elseif string.find(zone, "Northrend") or string.find(zone, "Outland") then
            optimalDistance = 45 -- High-level zones
        elseif string.find(zone, "Elwynn") or string.find(zone, "Dun Morogh") or
               string.find(zone, "Teldrassil") or string.find(zone, "Durotar") then
            optimalDistance = 35 -- Starting zones
        end
    end

    -- Ensure reasonable bounds
    optimalDistance = math.max(optimalDistance, 15)
    optimalDistance = math.min(optimalDistance, 100)

    return math.floor(optimalDistance)
end



-- Apply camera settings with forced WTF modification
function MaxCamEnhanced:ApplyCameraSettings()
    if not self.db.profile.enabled then
        return
    end

    -- Prevent multiple rapid calls
    local now = GetTime()
    if self.lastApplyTime and (now - self.lastApplyTime) < 1 then
        return -- Skip if called within 1 second
    end
    self.lastApplyTime = now

    local distance = self.db.profile.cameraDistance

    -- Auto-detect optimal distance if enabled (works continuously)
    if self.db.profile.autoDetectDistance then
        local autoDistance = self:AutoDetectOptimalDistance()
        if autoDistance ~= distance then
            distance = autoDistance
            self.db.profile.cameraDistance = distance
            -- Also update unified distance if unification is enabled
            if self.db.profile.unifyCameraDistance then
                self.db.profile.unifiedSavedDistance = math.max(1, distance - 5)
            end
        end
    end

    -- Apply unification if enabled - FORCE to WTF files
    if self.db.profile.unifyCameraDistance then
        local unifiedDistance = self.db.profile.unifiedSavedDistance

        -- Apply all settings with forced WTF save
        SetCVar("cameraDistanceMax", distance)
        SetCVar("cameraSavedDistance", unifiedDistance)

        -- Apply camera pitch if control is enabled (may not work in 3.3.5a)
        if self.db.profile.controlCameraPitch then
            local success = pcall(SetCVar, "cameraSavedPitch", self.db.profile.cameraPitch)
            if not success then
                -- Pitch control not supported in this version
                self.db.profile.controlCameraPitch = false
            end
        end

        -- Force to WTF files using ConsoleExec
        ConsoleExec("CameraDistanceMax " .. distance)

        -- Force save all CVars to WTF files
        ConsoleExec("cvar_save")

    else
        -- Just apply max distance if unification is disabled
        SetCVar("cameraDistanceMax", distance)
        ConsoleExec("CameraDistanceMax " .. distance)
    end
end


-- Apply settings to game (simplified like MouseSpeedEnhanced)
function MaxCamEnhanced:ApplySettings()
    if self.db.profile.enabled then
        self:ApplyCameraSettings()
    else
        -- Reset to default WoW camera distance using SetCVar
        SetCVar("cameraDistanceMax", "15")
    end

    -- Settings apply silently like MouseSpeedEnhanced
end

-- Reset to defaults (like MouseSpeedEnhanced)
function MaxCamEnhanced:ResetToDefaults()
    self.db:ResetProfile()
    self:ApplySettings()
    -- Silent reset like MouseSpeedEnhanced
end

-- Commands removed - use interface only

-- Update current values display in config
function MaxCamEnhanced:UpdateCurrentValues()
    -- Update configuration interface if it's open
    if LibStub and LibStub("AceConfigRegistry-3.0", true) then
        LibStub("AceConfigRegistry-3.0"):NotifyChange("MaxCamEnhanced")
    end
end

-- Configuration is handled in MaxCamEnhanced_Config.lua
