-- MaxCam Enhanced - Advanced camera distance control using Ace3
-- Version 2.0

-- Create addon using AceAddon-3.0
local MaxCamEnhanced = LibStub("AceAddon-3.0"):NewAddon("MaxCamEnhanced", "AceEvent-3.0", "AceConsole-3.0")
local L = MaxCamEnhanced_Locale or {}

-- Export to global scope
_G["MaxCamEnhanced"] = MaxCamEnhanced

-- Default settings
local defaults = {
    profile = {
        cameraDistance = 30,
        enabled = true,
        autoApply = true,
        applyOnZoneChange = true,
        autoDetectDistance = false,
        unifyCameraDistance = true,
        unifiedSavedDistance = 25,
        controlCameraPitch = false,
        cameraPitch = 20
    }
}

function MaxCamEnhanced:OnInitialize()
    -- Initialize database
    self.db = LibStub("AceDB-3.0"):New("MaxCamEnhancedDB", defaults, "Default")

    -- Commands removed - use interface only

    print("|cFF00FF00MaxCamEnhanced v2.0 loaded! Open Interface > AddOns > MaxCamEnhanced for settings.|r")
end

function MaxCamEnhanced:OnEnable()
    -- Register events
    self:RegisterEvent("PLAYER_ENTERING_WORLD")
    self:RegisterEvent("ZONE_CHANGED_NEW_AREA")

    -- Initialize configuration with small delay
    C_Timer.After(0.1, function()
        if MaxCamEnhanced.InitializeConfig then
            MaxCamEnhanced.InitializeConfig()
        end
    end)
end

function MaxCamEnhanced:OnDisable()
    -- Clean up configuration registration
    self.configInitialized = false
end

-- Event handler - Apply settings once on login
function MaxCamEnhanced:PLAYER_ENTERING_WORLD()
    -- Apply camera settings on login (includes unification if enabled)
    self:ApplyCameraSettings()

    -- Unregister event after first application
    self:UnregisterEvent("PLAYER_ENTERING_WORLD")
end

-- Force unification for all characters by overriding WTF values (silent check)
function MaxCamEnhanced:ForceUnifyAllCharacters()
    if not self.db.profile.unifyCameraDistance then
        return
    end

    local unifiedDistance = self.db.profile.unifiedSavedDistance
    local maxDistance = self.db.profile.cameraDistance

    -- Get current values to check if change is needed
    local currentSaved = GetCVar("cameraSavedDistance")
    local currentMax = GetCVar("cameraDistanceMax")
    local currentPitch = GetCVar("cameraSavedPitch")

    local needsUpdate = false

    -- Check if values need updating
    if not currentSaved or math.abs(tonumber(currentSaved) - unifiedDistance) > 0.1 then
        needsUpdate = true
    end
    if not currentMax or math.abs(tonumber(currentMax) - maxDistance) > 0.1 then
        needsUpdate = true
    end
    -- Check if pitch needs update (only if pitch control is enabled)
    if self.db.profile.controlCameraPitch then
        local targetPitch = tostring(self.db.profile.cameraPitch)
        if not currentPitch or tonumber(currentPitch) ~= tonumber(targetPitch) then
            needsUpdate = true
        end
    end

    if needsUpdate then
        -- Apply all values silently
        SetCVar("cameraSavedDistance", unifiedDistance)
        SetCVar("cameraDistanceMax", maxDistance)

        -- Apply pitch only if pitch control is enabled
        if self.db.profile.controlCameraPitch then
            local pitchValue = tostring(self.db.profile.cameraPitch)
            SetCVar("cameraSavedPitch", pitchValue)
            -- Force apply pitch immediately
            ConsoleExec("CameraSavedPitch " .. pitchValue)
        elseif not currentPitch or currentPitch == "" then
            -- Set default pitch only if not set and pitch control is disabled
            SetCVar("cameraSavedPitch", "20")
        end

        -- Force to WTF files
        ConsoleExec("CameraDistanceMax " .. maxDistance)
        ConsoleExec("CameraSavedDistance " .. unifiedDistance)
        ConsoleExec("cvar_save")

        -- Update config display
        self:UpdateCurrentValues()
    end
end

function MaxCamEnhanced:ZONE_CHANGED_NEW_AREA()
    -- Apply camera distance when changing zones if enabled
    if self.db.profile.applyOnZoneChange then
        self:ApplyCameraSettings()
    end
end

-- Auto-detect optimal camera distance based on character's saved distance
function MaxCamEnhanced:AutoDetectOptimalDistance()
    local savedDistance = GetCVar("cameraSavedDistance")
    if not savedDistance then
        return 20 -- Default fallback
    end

    local currentDistance = tonumber(savedDistance)
    if not currentDistance then
        return 20 -- Default fallback
    end

    -- Calculate optimal max distance with buffer
    local optimalDistance
    if currentDistance <= 15 then
        -- Character prefers close camera, give moderate buffer
        optimalDistance = 25
    elseif currentDistance <= 20 then
        -- Character uses standard distance, give good buffer
        optimalDistance = 30
    elseif currentDistance <= 25 then
        -- Character prefers far camera, give larger buffer
        optimalDistance = 35
    else
        -- Character uses very far camera, give maximum buffer
        optimalDistance = math.min(currentDistance + 15, 100)
    end

    return math.floor(optimalDistance)
end

-- Apply camera settings with forced WTF modification
function MaxCamEnhanced:ApplyCameraSettings()
    if not self.db.profile.enabled then
        return
    end

    -- Prevent multiple rapid calls
    local now = GetTime()
    if self.lastApplyTime and (now - self.lastApplyTime) < 1 then
        return -- Skip if called within 1 second
    end
    self.lastApplyTime = now

    local distance = self.db.profile.cameraDistance

    -- Auto-detect optimal distance if enabled and unify is disabled
    if self.db.profile.autoDetectDistance and not self.db.profile.unifyCameraDistance then
        local autoDistance = self:AutoDetectOptimalDistance()
        if autoDistance ~= distance then
            distance = autoDistance
            self.db.profile.cameraDistance = distance
        end
    end

    -- Apply unification if enabled - FORCE to WTF files
    if self.db.profile.unifyCameraDistance then
        local unifiedDistance = self.db.profile.unifiedSavedDistance

        -- Apply all settings with forced WTF save
        SetCVar("cameraDistanceMax", distance)
        SetCVar("cameraSavedDistance", unifiedDistance)

        -- Apply pitch based on settings
        local currentPitch = GetCVar("cameraSavedPitch")
        if self.db.profile.controlCameraPitch then
            local pitchValue = tostring(self.db.profile.cameraPitch)
            SetCVar("cameraSavedPitch", pitchValue)
            -- Force apply pitch immediately
            ConsoleExec("CameraSavedPitch " .. pitchValue)
        elseif not currentPitch or currentPitch == "" then
            -- Set default pitch only if not set and pitch control is disabled
            SetCVar("cameraSavedPitch", "20")
        end

        -- Force to WTF files using ConsoleExec
        ConsoleExec("CameraDistanceMax " .. distance)

        -- Force save all CVars to WTF files
        ConsoleExec("cvar_save")

        print("|cFF00FF00MaxCamEnhanced:|r Camera distance set to " .. distance .. " (both default position and max zoom)")
    else
        -- Just apply max distance if unification is disabled
        SetCVar("cameraDistanceMax", distance)
        ConsoleExec("CameraDistanceMax " .. distance)
        print("|cFF00FF00MaxCamEnhanced:|r Maximum camera distance set to " .. distance)
    end
end

-- Force save current CVars to WTF files using multiple methods
function MaxCamEnhanced:ForceWTFSave()
    -- Method 1: Use ConsoleExec for camera distance (most reliable)
    local maxDist = GetCVar("cameraDistanceMax")
    if maxDist then
        ConsoleExec("CameraDistanceMax " .. maxDist)
    end

    -- Method 2: Force save all CVars
    ConsoleExec("cvar_save")

    -- Method 3: Set CVars with forced persistence flag
    local savedDist = GetCVar("cameraSavedDistance")
    local savedPitch = GetCVar("cameraSavedPitch")
    if savedDist then
        SetCVar("cameraSavedDistance", savedDist, "1")  -- Force save flag
    end
    if savedPitch then
        SetCVar("cameraSavedPitch", savedPitch, "1")  -- Force save flag
    end

    -- Method 4: Try alternative console commands
    if maxDist then
        ConsoleExec("cameraDistanceMaxFactor " .. (tonumber(maxDist) / 15))  -- Factor relative to default
    end

    -- Method 5: Force reload of camera settings
    ConsoleExec("cvar_reload")
end

-- Apply settings to game (simplified like MouseSpeedEnhanced)
function MaxCamEnhanced:ApplySettings()
    if self.db.profile.enabled then
        self:ApplyCameraSettings()
    else
        -- Reset to default WoW camera distance using SetCVar
        SetCVar("cameraDistanceMax", "15")
    end

    -- Settings apply silently like MouseSpeedEnhanced
end

-- Reset to defaults (like MouseSpeedEnhanced)
function MaxCamEnhanced:ResetToDefaults()
    self.db:ResetProfile()
    self:ApplySettings()
    -- Silent reset like MouseSpeedEnhanced
end

-- Commands removed - use interface only

-- Update current values display in config
function MaxCamEnhanced:UpdateCurrentValues()
    -- Обновляем конфигурационный интерфейс если он открыт
    if LibStub and LibStub("AceConfigRegistry-3.0", true) then
        LibStub("AceConfigRegistry-3.0"):NotifyChange("MaxCamEnhanced")
    end
end

-- Configuration is handled in MaxCamEnhanced_Config.lua
