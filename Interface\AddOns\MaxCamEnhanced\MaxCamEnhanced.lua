-- MaxCam Enhanced - Advanced camera distance control using Ace3
-- Version 2.0

-- Create addon using AceAddon-3.0
local MaxCamEnhanced = LibStub("AceAddon-3.0"):NewAddon("MaxCamEnhanced", "AceEvent-3.0", "AceConsole-3.0")
local L = MaxCamEnhanced_Locale or {}

-- Export to global scope
_G["MaxCamEnhanced"] = MaxCamEnhanced

-- Default settings
local defaults = {
    profile = {
        cameraDistance = 30,
        enabled = true,
        autoApply = true,
        applyOnZoneChange = true,
        autoDetectDistance = false,
        unifyCameraDistance = true,
        unifiedSavedDistance = 25
    }
}

function MaxCamEnhanced:OnInitialize()
    -- Initialize database
    self.db = LibStub("AceDB-3.0"):New("MaxCamEnhancedDB", defaults, "Default")

    -- Commands removed - use interface only

    print("|cFF00FF00MaxCamEnhanced v2.0 loaded! Open Interface > AddOns > MaxCamEnhanced for settings.|r")
end

function MaxCamEnhanced:OnEnable()
    -- Register events
    self:RegisterEvent("PLAYER_ENTERING_WORLD")
    self:RegisterEvent("ZONE_CHANGED_NEW_AREA")

    -- Initialize configuration with small delay
    C_Timer.After(0.1, function()
        if MaxCamEnhanced.InitializeConfig then
            MaxCamEnhanced.InitializeConfig()
        end
    end)
end

function MaxCamEnhanced:OnDisable()
    -- Clean up configuration registration
    self.configInitialized = false
end

-- Event handler - Apply settings once on login
function MaxCamEnhanced:PLAYER_ENTERING_WORLD()
    -- Apply camera settings on login (includes unification if enabled)
    self:ApplyCameraSettings()

    -- Unregister event after first application
    self:UnregisterEvent("PLAYER_ENTERING_WORLD")
end

-- Force unification for all characters by overriding WTF values (silent check)
function MaxCamEnhanced:ForceUnifyAllCharacters()
    if not self.db.profile.unifyCameraDistance then
        return
    end

    local unifiedDistance = self.db.profile.unifiedSavedDistance
    local maxDistance = math.max(unifiedDistance, self.db.profile.cameraDistance)

    -- Get current values to check if change is needed
    local currentSaved = GetCVar("cameraSavedDistance")
    local currentMax = GetCVar("cameraDistanceMax")
    local currentPitch = GetCVar("cameraSavedPitch")

    local needsUpdate = false

    -- Check if values need updating
    if not currentSaved or math.abs(tonumber(currentSaved) - unifiedDistance) > 0.1 then
        needsUpdate = true
    end
    if not currentMax or math.abs(tonumber(currentMax) - maxDistance) > 0.1 then
        needsUpdate = true
    end
    if not currentPitch or tonumber(currentPitch) ~= 20 then
        needsUpdate = true
    end

    if needsUpdate then
        -- Apply all values silently
        SetCVar("cameraSavedDistance", unifiedDistance)
        SetCVar("cameraDistanceMax", maxDistance)
        SetCVar("cameraSavedPitch", "20")

        -- Force to WTF files
        ConsoleExec("CameraDistanceMax " .. maxDistance)
        ConsoleExec("cvar_save")

        print("|cFF00FF00MaxCamEnhanced:|r Camera unified - Distance: " .. unifiedDistance .. ", Max: " .. maxDistance)
    end
end

function MaxCamEnhanced:ZONE_CHANGED_NEW_AREA()
    -- Apply camera distance when changing zones if enabled
    if self.db.profile.applyOnZoneChange then
        self:ApplyCameraSettings()
    end
end

-- Auto-detect optimal camera distance based on character's saved distance
function MaxCamEnhanced:AutoDetectOptimalDistance()
    local savedDistance = GetCVar("cameraSavedDistance")
    if not savedDistance then
        return 20 -- Default fallback
    end

    local currentDistance = tonumber(savedDistance)
    if not currentDistance then
        return 20 -- Default fallback
    end

    -- Calculate optimal max distance with buffer
    local optimalDistance
    if currentDistance <= 15 then
        -- Character prefers close camera, give moderate buffer
        optimalDistance = 25
    elseif currentDistance <= 20 then
        -- Character uses standard distance, give good buffer
        optimalDistance = 30
    elseif currentDistance <= 25 then
        -- Character prefers far camera, give larger buffer
        optimalDistance = 35
    else
        -- Character uses very far camera, give maximum buffer
        optimalDistance = math.min(currentDistance + 15, 100)
    end

    return math.floor(optimalDistance)
end

-- Apply camera settings with forced WTF modification
function MaxCamEnhanced:ApplyCameraSettings()
    if not self.db.profile.enabled then
        return
    end

    -- Prevent multiple rapid calls
    local now = GetTime()
    if self.lastApplyTime and (now - self.lastApplyTime) < 1 then
        return -- Skip if called within 1 second
    end
    self.lastApplyTime = now

    local distance = self.db.profile.cameraDistance

    -- Auto-detect optimal distance if enabled and unify is disabled
    if self.db.profile.autoDetectDistance and not self.db.profile.unifyCameraDistance then
        local autoDistance = self:AutoDetectOptimalDistance()
        if autoDistance ~= distance then
            distance = autoDistance
            self.db.profile.cameraDistance = distance
        end
    end

    -- Apply unification if enabled - FORCE to WTF files
    if self.db.profile.unifyCameraDistance then
        local unifiedDistance = self.db.profile.unifiedSavedDistance

        -- Ensure max distance is always higher than unified distance
        if distance <= unifiedDistance then
            distance = unifiedDistance + 5
            self.db.profile.cameraDistance = distance
            print("|cFF00FF00MaxCamEnhanced:|r Auto-corrected max distance to " .. distance)
        end

        -- Apply all settings with forced WTF save
        SetCVar("cameraDistanceMax", distance)
        SetCVar("cameraSavedDistance", unifiedDistance)
        SetCVar("cameraSavedPitch", "20")

        -- Force to WTF files using ConsoleExec
        ConsoleExec("CameraDistanceMax " .. distance)

        -- Force save all CVars to WTF files
        ConsoleExec("cvar_save")

        print("|cFF00FF00MaxCamEnhanced:|r Camera unified - Distance: " .. unifiedDistance .. ", Max: " .. distance .. " (saved to WTF)")
    else
        -- Just apply max distance if unification is disabled
        SetCVar("cameraDistanceMax", distance)
        ConsoleExec("CameraDistanceMax " .. distance)
    end
end

-- Force save current CVars to WTF files using multiple methods
function MaxCamEnhanced:ForceWTFSave()
    -- Method 1: Use ConsoleExec for camera distance (most reliable)
    local maxDist = GetCVar("cameraDistanceMax")
    if maxDist then
        ConsoleExec("CameraDistanceMax " .. maxDist)
    end

    -- Method 2: Force save all CVars
    ConsoleExec("cvar_save")

    -- Method 3: Set CVars with forced persistence flag
    local savedDist = GetCVar("cameraSavedDistance")
    local savedPitch = GetCVar("cameraSavedPitch")
    if savedDist then
        SetCVar("cameraSavedDistance", savedDist, "1")  -- Force save flag
    end
    if savedPitch then
        SetCVar("cameraSavedPitch", savedPitch, "1")  -- Force save flag
    end

    -- Method 4: Try alternative console commands
    if maxDist then
        ConsoleExec("cameraDistanceMaxFactor " .. (tonumber(maxDist) / 15))  -- Factor relative to default
    end

    -- Method 5: Force reload of camera settings
    ConsoleExec("cvar_reload")
end

-- Apply settings to game (simplified like MouseSpeedEnhanced)
function MaxCamEnhanced:ApplySettings()
    if self.db.profile.enabled then
        self:ApplyCameraSettings()
    else
        -- Reset to default WoW camera distance using SetCVar
        SetCVar("cameraDistanceMax", "15")
    end

    -- Settings apply silently like MouseSpeedEnhanced
end

-- Reset to defaults (like MouseSpeedEnhanced)
function MaxCamEnhanced:ResetToDefaults()
    self.db:ResetProfile()
    self:ApplySettings()
    -- Silent reset like MouseSpeedEnhanced
end

-- Commands removed - use interface only


-- Initialize configuration interface
function MaxCamEnhanced.InitializeConfig()
    if MaxCamEnhanced.configInitialized then
        return
    end

    local AceConfig = LibStub("AceConfig-3.0")
    local AceConfigDialog = LibStub("AceConfigDialog-3.0")

    local options = {
        name = "MaxCamEnhanced",
        handler = MaxCamEnhanced,
        type = "group",
        args = {
            header = {
                order = 1,
                type = "header",
                name = "MaxCamEnhanced Settings",
            },
            enabled = {
                order = 2,
                type = "toggle",
                name = "Enable Camera Distance",
                desc = "Enable/disable camera distance modifications",
                get = function() return MaxCamEnhanced.db.profile.enabled end,
                set = function(info, val)
                    MaxCamEnhanced.db.profile.enabled = val
                    if val then
                        MaxCamEnhanced:ApplySettings()
                    end
                end,
            },
            autoApply = {
                order = 3,
                type = "toggle",
                name = "Auto Apply",
                desc = "Automatically apply camera settings on login and zone changes",
                get = function() return MaxCamEnhanced.db.profile.autoApply end,
                set = function(info, val) MaxCamEnhanced.db.profile.autoApply = val end,
            },
            unifyCameraDistance = {
                order = 4,
                type = "toggle",
                name = "Unify Camera Distance",
                desc = "Apply uniform camera settings across all characters",
                get = function() return MaxCamEnhanced.db.profile.unifyCameraDistance end,
                set = function(info, val)
                    MaxCamEnhanced.db.profile.unifyCameraDistance = val
                    if val then
                        MaxCamEnhanced:ApplySettings()
                    end
                end,
            },
            autoDetectDistance = {
                order = 5,
                type = "toggle",
                name = "Auto-detect Optimal Distance",
                desc = "Automatically determine optimal camera distance per character",
                get = function() return MaxCamEnhanced.db.profile.autoDetectDistance end,
                set = function(info, val) MaxCamEnhanced.db.profile.autoDetectDistance = val end,
            },
            spacer1 = {
                order = 10,
                type = "header",
                name = "Camera Distance Settings",
            },
            maxDistance = {
                order = 11,
                type = "range",
                name = "Maximum Camera Distance",
                desc = "Set maximum camera distance limit - this will be unified across all characters (1-100)",
                min = 1,
                max = 100,
                step = 1,
                get = function() return MaxCamEnhanced.db.profile.cameraDistance or 30 end,
                set = function(info, val)
                    MaxCamEnhanced.db.profile.cameraDistance = val
                    -- Also update unified distance to be slightly less than max
                    MaxCamEnhanced.db.profile.unifiedSavedDistance = math.max(1, val - 5)
                    if MaxCamEnhanced.db.profile.enabled then
                        MaxCamEnhanced:ApplySettings()
                    end
                end,
            },
            unifiedDistance = {
                order = 12,
                type = "range",
                name = "Default Camera Position",
                desc = "Where camera sits by default (should be less than maximum) (1-95)",
                min = 1,
                max = 95,
                step = 1,
                get = function() return MaxCamEnhanced.db.profile.unifiedSavedDistance or 25 end,
                set = function(info, val)
                    MaxCamEnhanced.db.profile.unifiedSavedDistance = val
                    -- Ensure max distance is always higher
                    if val >= MaxCamEnhanced.db.profile.cameraDistance then
                        MaxCamEnhanced.db.profile.cameraDistance = val + 5
                    end
                    if MaxCamEnhanced.db.profile.enabled then
                        MaxCamEnhanced:ApplySettings()
                    end
                end,
            },
            applyOnZoneChange = {
                order = 13,
                type = "toggle",
                name = "Apply on Zone Change",
                desc = "Automatically apply camera settings when changing zones",
                get = function() return MaxCamEnhanced.db.profile.applyOnZoneChange end,
                set = function(info, val) MaxCamEnhanced.db.profile.applyOnZoneChange = val end,
            },
            spacer2 = {
                order = 20,
                type = "header",
                name = "Actions",
            },
            applyNow = {
                order = 21,
                type = "execute",
                name = "Apply Settings Now",
                desc = "Apply current camera settings immediately",
                func = function() MaxCamEnhanced:ApplySettings() end,
            },
            resetDefaults = {
                order = 22,
                type = "execute",
                name = "Reset to Defaults",
                desc = "Reset all settings to default values",
                func = function()
                    MaxCamEnhanced.db:ResetProfile()
                    print("MaxCamEnhanced: Reset to defaults")
                end,
            },
        },
    }

    AceConfig:RegisterOptionsTable("MaxCamEnhanced", options)
    AceConfigDialog:AddToBlizOptions("MaxCamEnhanced", "MaxCamEnhanced")

    MaxCamEnhanced.configInitialized = true
    print("MaxCamEnhanced: Configuration interface initialized")
end
