
SwatterData = {
	["enabled"] = true,
	["autoshow"] = true,
	["errors"] = {
		{
			["message"] = "...ootLog\\LibDBIcon-1.0\\LibDBIcon-1.0\\LibDBIcon-1.0.lua:485: attempt to call method 'SetToFinalAlpha' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.5b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  epgp, v5.5.13\n  epgplootmaster, v0.4.9\n  epgplootmasterml, v0.4.9\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.1\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=55c)\n",
			["timestamp"] = "2025-06-23 06:06:20",
			["context"] = "Global",
			["stack"] = "[C]: in function `SetToFinalAlpha'\n...ootLog\\LibDBIcon-1.0\\LibDBIcon-1.0\\LibDBIcon-1.0.lua:485: in main chunk\n",
		}, -- [1]
		{
			["message"] = "...ootLog\\LibDBIcon-1.0\\LibDBIcon-1.0\\LibDBIcon-1.0.lua:221: attempt to call method 'SetFixedFrameStrata' (a nil value)",
			["count"] = 5,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.5b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  epgp, v5.5.13\n  epgplootmaster, v0.4.9\n  epgplootmasterml, v0.4.9\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.1\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=55c)\n",
			["timestamp"] = "2025-06-23 06:06:20",
			["context"] = "Global",
			["stack"] = "[C]: in function `SetFixedFrameStrata'\n...ootLog\\LibDBIcon-1.0\\LibDBIcon-1.0\\LibDBIcon-1.0.lua:221: in function <...ootLog\\LibDBIcon-1.0\\LibDBIcon-1.0\\LibDBIcon-1.0.lua:216>\n...ootLog\\LibDBIcon-1.0\\LibDBIcon-1.0\\LibDBIcon-1.0.lua:333: in function `Register'\nInterface\\AddOns\\LootLog\\LootLog.lua:165: in function <Interface\\AddOns\\LootLog\\LootLog.lua:117>\n",
		}, -- [2]
		{
			["message"] = "...teBuffs\\libs\\LibNameplates-1.0\\LibNameplates-1.0.lua:224: attempt to index local 'frame' (a nil value)",
			["count"] = 29,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.5b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  epgp, v5.5.13\n  epgplootmaster, v0.4.9\n  epgplootmasterml, v0.4.9\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.1\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, vr301\n  SkadaAbsorbs, v1.0\n  SkadaCC, v1.0\n  SkadaDamage, v1.0\n  SkadaDamageTaken, v1.0\n  SkadaDeaths, v1.0\n  SkadaDebuffs, v1.0\n  SkadaDispels, v1.0\n  SkadaEnemies, v1.0\n  SkadaFailbot, v1.0\n  SkadaHealing, v1.0\n  SkadaPower, v1.0\n  SkadaThreat, v1.0\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a9e)\n",
			["timestamp"] = "2025-06-23 06:06:26",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n...teBuffs\\libs\\LibNameplates-1.0\\LibNameplates-1.0.lua:224: in function `GetNameRegion'\n...teBuffs\\libs\\LibNameplates-1.0\\LibNameplates-1.0.lua:90: in function <...teBuffs\\libs\\LibNameplates-1.0\\LibNameplates-1.0.lua:89>\n...teBuffs\\libs\\LibNameplates-1.0\\LibNameplates-1.0.lua:969: in function <...teBuffs\\libs\\LibNameplates-1.0\\LibNameplates-1.0.lua:967>\n(tail call): ?\nInterface\\AddOns\\PlateBuffs\\core.lua:398: in function `ShouldAddBuffs'\nInterface\\AddOns\\PlateBuffs\\core.lua:451: in function `?'\n...Icon-1.0\\CallbackHandler-1.0\\CallbackHandler-1.0.lua:119: in function <...Icon-1.0\\CallbackHandler-1.0\\CallbackHandler-1.0.lua:119>\n[C]: ?\n...Icon-1.0\\CallbackHandler-1.0\\CallbackHandler-1.0.lua:29: in function <...Icon-1.0\\CallbackHandler-1.0\\CallbackHandler-1.0.lua:25>\n...Icon-1.0\\CallbackHandler-1.0\\CallbackHandler-1.0.lua:64: in function `Fire'\n...teBuffs\\libs\\LibNameplates-1.0\\LibNameplates-1.0.lua:696: in function `SetupNameplate'\n...teBuffs\\libs\\LibNameplates-1.0\\LibNameplates-1.0.lua:670: in function `NameplateFirstLoad'\n...teBuffs\\libs\\LibNameplates-1.0\\LibNameplates-1.0.lua:206: in function <...teBuffs\\libs\\LibNameplates-1.0\\LibNameplates-1.0.lua:203>\n(tail call): ?\n(tail call): ?\n(tail call): ?\n(tail call): ?\n(tail call): ?\n...teBuffs\\libs\\LibNameplates-1.0\\LibNameplates-1.0.lua:216: in function <...teBuffs\\libs\\LibNameplates-1.0\\LibNameplates-1.0.lua:212>\n...teBuffs\\libs\\LibNameplates-1.0\\LibNameplates-1.0.lua:796: in function <...teBuffs\\libs\\LibNameplates-1.0\\LibNameplates-1.0.lua:796>\n",
		}, -- [3]
		{
			["message"] = "Interface\\AddOns\\PlateBuffs\\combatlog.lua:168: GetSpellInfo(): Invalid spell slot",
			["count"] = 3,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.5b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMPartyClassic, v\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  epgp, v5.5.13\n  epgplootmaster, v0.4.9\n  epgplootmasterml, v0.4.9\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.1\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, vr301\n  SkadaAbsorbs, v1.0\n  SkadaCC, v1.0\n  SkadaDamage, v1.0\n  SkadaDamageTaken, v1.0\n  SkadaDeaths, v1.0\n  SkadaDebuffs, v1.0\n  SkadaDispels, v1.0\n  SkadaEnemies, v1.0\n  SkadaFailbot, v1.0\n  SkadaHealing, v1.0\n  SkadaPower, v1.0\n  SkadaThreat, v1.0\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=ab3)\n",
			["timestamp"] = "2025-06-23 06:06:37",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n[C]: ?\nInterface\\AddOns\\PlateBuffs\\combatlog.lua:168: in function `?'\n...Icon-1.0\\CallbackHandler-1.0\\CallbackHandler-1.0.lua:119: in function <...Icon-1.0\\CallbackHandler-1.0\\CallbackHandler-1.0.lua:119>\n[C]: ?\n...Icon-1.0\\CallbackHandler-1.0\\CallbackHandler-1.0.lua:29: in function <...Icon-1.0\\CallbackHandler-1.0\\CallbackHandler-1.0.lua:25>\n...Icon-1.0\\CallbackHandler-1.0\\CallbackHandler-1.0.lua:64: in function `Fire'\n...\\PlateBuffs\\libs\\LibAuraInfo-1.0\\LibAuraInfo-1.0.lua:1065: in function `?'\n...\\PlateBuffs\\libs\\LibAuraInfo-1.0\\LibAuraInfo-1.0.lua:782: in function `?'\n...\\PlateBuffs\\libs\\LibAuraInfo-1.0\\LibAuraInfo-1.0.lua:237: in function <...\\PlateBuffs\\libs\\LibAuraInfo-1.0\\LibAuraInfo-1.0.lua:235>\n",
		}, -- [4]
		{
			["message"] = "Interface\\AddOns\\LootLog\\ItemFrame.lua:116: attempt to call method 'SetItemByID' (a nil value)",
			["count"] = 12,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.5b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMPartyClassic, v\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  epgp, v5.5.13\n  epgplootmaster, v0.4.9\n  epgplootmasterml, v0.4.9\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.1\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, vr301\n  SkadaAbsorbs, v1.0\n  SkadaCC, v1.0\n  SkadaDamage, v1.0\n  SkadaDamageTaken, v1.0\n  SkadaDeaths, v1.0\n  SkadaDebuffs, v1.0\n  SkadaDispels, v1.0\n  SkadaEnemies, v1.0\n  SkadaFailbot, v1.0\n  SkadaHealing, v1.0\n  SkadaPower, v1.0\n  SkadaThreat, v1.0\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=ab3)\n",
			["timestamp"] = "2025-06-23 06:06:42",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `SetItemByID'\nInterface\\AddOns\\LootLog\\ItemFrame.lua:116: in function <Interface\\AddOns\\LootLog\\ItemFrame.lua:116>\n",
		}, -- [5]
		{
			["message"] = "...ootLog\\LibDBIcon-1.0\\LibDBIcon-1.0\\LibDBIcon-1.0.lua:485: attempt to call method 'SetToFinalAlpha' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.5b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  epgp, v5.5.13\n  epgplootmaster, v0.4.9\n  epgplootmasterml, v0.4.9\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.1\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=55c)\n",
			["timestamp"] = "2025-06-23 06:09:34",
			["context"] = "Global",
			["stack"] = "[C]: in function `SetToFinalAlpha'\n...ootLog\\LibDBIcon-1.0\\LibDBIcon-1.0\\LibDBIcon-1.0.lua:485: in main chunk\n",
		}, -- [6]
		{
			["message"] = "...ootLog\\LibDBIcon-1.0\\LibDBIcon-1.0\\LibDBIcon-1.0.lua:221: attempt to call method 'SetFixedFrameStrata' (a nil value)",
			["count"] = 5,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.5b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  epgp, v5.5.13\n  epgplootmaster, v0.4.9\n  epgplootmasterml, v0.4.9\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.1\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=55c)\n",
			["timestamp"] = "2025-06-23 06:09:34",
			["context"] = "Global",
			["stack"] = "[C]: in function `SetFixedFrameStrata'\n...ootLog\\LibDBIcon-1.0\\LibDBIcon-1.0\\LibDBIcon-1.0.lua:221: in function <...ootLog\\LibDBIcon-1.0\\LibDBIcon-1.0\\LibDBIcon-1.0.lua:216>\n...ootLog\\LibDBIcon-1.0\\LibDBIcon-1.0\\LibDBIcon-1.0.lua:333: in function `Register'\nInterface\\AddOns\\LootLog\\LootLog.lua:165: in function <Interface\\AddOns\\LootLog\\LootLog.lua:117>\n",
		}, -- [7]
		{
			["message"] = "Interface\\AddOns\\LootLog\\ItemFrame.lua:116: attempt to call method 'SetItemByID' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.5b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMPartyClassic, v\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  epgp, v5.5.13\n  epgplootmaster, v0.4.9\n  epgplootmasterml, v0.4.9\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.1\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, vr301\n  SkadaAbsorbs, v1.0\n  SkadaCC, v1.0\n  SkadaDamage, v1.0\n  SkadaDamageTaken, v1.0\n  SkadaDeaths, v1.0\n  SkadaDebuffs, v1.0\n  SkadaDispels, v1.0\n  SkadaEnemies, v1.0\n  SkadaFailbot, v1.0\n  SkadaHealing, v1.0\n  SkadaPower, v1.0\n  SkadaThreat, v1.0\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=ab3)\n",
			["timestamp"] = "2025-06-23 06:17:53",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `SetItemByID'\nInterface\\AddOns\\LootLog\\ItemFrame.lua:116: in function <Interface\\AddOns\\LootLog\\ItemFrame.lua:116>\n",
		}, -- [8]
		{
			["message"] = "Interface\\AddOns\\LootLog\\ItemFrame.lua:116: attempt to call method 'SetItemByID' (a nil value)",
			["count"] = 6,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.5b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMPartyClassic, v\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  epgp, v5.5.13\n  epgplootmaster, v0.4.9\n  epgplootmasterml, v0.4.9\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.1\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, vr301\n  SkadaAbsorbs, v1.0\n  SkadaCC, v1.0\n  SkadaDamage, v1.0\n  SkadaDamageTaken, v1.0\n  SkadaDeaths, v1.0\n  SkadaDebuffs, v1.0\n  SkadaDispels, v1.0\n  SkadaEnemies, v1.0\n  SkadaFailbot, v1.0\n  SkadaHealing, v1.0\n  SkadaPower, v1.0\n  SkadaThreat, v1.0\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=ab3)\n",
			["timestamp"] = "2025-06-23 06:42:09",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `SetItemByID'\nInterface\\AddOns\\LootLog\\ItemFrame.lua:116: in function <Interface\\AddOns\\LootLog\\ItemFrame.lua:116>\n",
		}, -- [9]
		{
			["message"] = "Interface\\AddOns\\LootLog\\ItemFrame.lua:116: attempt to call method 'SetItemByID' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.5b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMPartyClassic, v\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  epgp, v5.5.13\n  epgplootmaster, v0.4.9\n  epgplootmasterml, v0.4.9\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.1\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, vr301\n  SkadaAbsorbs, v1.0\n  SkadaCC, v1.0\n  SkadaDamage, v1.0\n  SkadaDamageTaken, v1.0\n  SkadaDeaths, v1.0\n  SkadaDebuffs, v1.0\n  SkadaDispels, v1.0\n  SkadaEnemies, v1.0\n  SkadaFailbot, v1.0\n  SkadaHealing, v1.0\n  SkadaPower, v1.0\n  SkadaThreat, v1.0\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=ab3)\n",
			["timestamp"] = "2025-06-23 06:42:54",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `SetItemByID'\nInterface\\AddOns\\LootLog\\ItemFrame.lua:116: in function <Interface\\AddOns\\LootLog\\ItemFrame.lua:116>\n",
		}, -- [10]
		{
			["message"] = "Interface\\AddOns\\LootLog\\ItemFrame.lua:116: attempt to call method 'SetItemByID' (a nil value)",
			["count"] = 2,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.5b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMPartyClassic, v\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  epgp, v5.5.13\n  epgplootmaster, v0.4.9\n  epgplootmasterml, v0.4.9\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.1\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, vr301\n  SkadaAbsorbs, v1.0\n  SkadaCC, v1.0\n  SkadaDamage, v1.0\n  SkadaDamageTaken, v1.0\n  SkadaDeaths, v1.0\n  SkadaDebuffs, v1.0\n  SkadaDispels, v1.0\n  SkadaEnemies, v1.0\n  SkadaFailbot, v1.0\n  SkadaHealing, v1.0\n  SkadaPower, v1.0\n  SkadaThreat, v1.0\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=ab3)\n",
			["timestamp"] = "2025-06-23 06:45:19",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `SetItemByID'\nInterface\\AddOns\\LootLog\\ItemFrame.lua:116: in function <Interface\\AddOns\\LootLog\\ItemFrame.lua:116>\n",
		}, -- [11]
		{
			["message"] = "Interface\\AddOns\\LootLog\\ItemFrame.lua:51: attempt to call method 'SetColorTexture' (a nil value)",
			["count"] = 1,
			["addons"] = "  BlizzBugsSuck, v3.3.5.9\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  AtlasQuest, v4.4.3\n  Babylonian, v5.1.DEV.130\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.1\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=3ef)\n",
			["timestamp"] = "2025-06-23 07:39:11",
			["context"] = "Global",
			["stack"] = "[C]: in function `SetColorTexture'\nInterface\\AddOns\\LootLog\\ItemFrame.lua:51: in function `initialize'\nInterface\\AddOns\\LootLog\\ItemFrame.lua:86: in function `CreateItemFrame'\nInterface\\AddOns\\LootLog\\LootLog.lua:249: in function `init'\nInterface\\AddOns\\LootLog\\LootLog.lua:398: in main chunk\n",
		}, -- [12]
		{
			["message"] = "Interface\\FrameXML\\UIDropDownMenu.lua:861: attempt to index local 'frame' (a nil value)",
			["count"] = 1,
			["addons"] = "  BlizzBugsSuck, v3.3.5.9\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  AtlasQuest, v4.4.3\n  Babylonian, v5.1.DEV.130\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.1\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=3ef)\n",
			["timestamp"] = "2025-06-23 07:39:11",
			["context"] = "Global",
			["stack"] = "[C]: ?\nInterface\\FrameXML\\UIDropDownMenu.lua:861: in function `UIDropDownMenu_SetText'\nInterface\\AddOns\\LootLog\\LootLog.lua:168: in function <Interface\\AddOns\\LootLog\\LootLog.lua:117>\n",
		}, -- [13]
		{
			["message"] = "Interface\\AddOns\\LootLog\\ItemFrame.lua:51: attempt to call method 'SetColorTexture' (a nil value)",
			["count"] = 1,
			["addons"] = "  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  AtlasQuest, v4.4.3\n  Babylonian, v5.1.DEV.130\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.1\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=2a6)\n",
			["timestamp"] = "2025-06-23 07:39:47",
			["context"] = "Global",
			["stack"] = "[C]: in function `SetColorTexture'\nInterface\\AddOns\\LootLog\\ItemFrame.lua:51: in function `initialize'\nInterface\\AddOns\\LootLog\\ItemFrame.lua:86: in function `CreateItemFrame'\nInterface\\AddOns\\LootLog\\LootLog.lua:249: in function `init'\nInterface\\AddOns\\LootLog\\LootLog.lua:398: in main chunk\n",
		}, -- [14]
		{
			["message"] = "Interface\\FrameXML\\UIDropDownMenu.lua:861: attempt to index local 'frame' (a nil value)",
			["count"] = 1,
			["addons"] = "  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  AtlasQuest, v4.4.3\n  Babylonian, v5.1.DEV.130\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.1\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=2a6)\n",
			["timestamp"] = "2025-06-23 07:39:47",
			["context"] = "Global",
			["stack"] = "[C]: ?\nInterface\\FrameXML\\UIDropDownMenu.lua:861: in function `UIDropDownMenu_SetText'\nInterface\\AddOns\\LootLog\\LootLog.lua:168: in function <Interface\\AddOns\\LootLog\\LootLog.lua:117>\n",
		}, -- [15]
		{
			["message"] = "Interface\\AddOns\\LootLog\\ItemFrame.lua:51: attempt to call method 'SetColorTexture' (a nil value)",
			["count"] = 1,
			["addons"] = "  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  AtlasQuest, v4.4.3\n  Babylonian, v5.1.DEV.130\n  Configator, v5.1.DEV.190\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.1\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=1c2)\n",
			["timestamp"] = "2025-06-23 07:40:24",
			["context"] = "Global",
			["stack"] = "[C]: in function `SetColorTexture'\nInterface\\AddOns\\LootLog\\ItemFrame.lua:51: in function `initialize'\nInterface\\AddOns\\LootLog\\ItemFrame.lua:86: in function `CreateItemFrame'\nInterface\\AddOns\\LootLog\\LootLog.lua:249: in function `init'\nInterface\\AddOns\\LootLog\\LootLog.lua:398: in main chunk\n",
		}, -- [16]
		{
			["message"] = "Interface\\FrameXML\\UIDropDownMenu.lua:861: attempt to index local 'frame' (a nil value)",
			["count"] = 1,
			["addons"] = "  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  AtlasQuest, v4.4.3\n  Babylonian, v5.1.DEV.130\n  Configator, v5.1.DEV.190\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.1\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=1c2)\n",
			["timestamp"] = "2025-06-23 07:40:24",
			["context"] = "Global",
			["stack"] = "[C]: ?\nInterface\\FrameXML\\UIDropDownMenu.lua:861: in function `UIDropDownMenu_SetText'\nInterface\\AddOns\\LootLog\\LootLog.lua:168: in function <Interface\\AddOns\\LootLog\\LootLog.lua:117>\n",
		}, -- [17]
		{
			["message"] = "Interface\\AddOns\\LootLog\\ItemFrame.lua:51: attempt to call method 'SetColorTexture' (a nil value)",
			["count"] = 1,
			["addons"] = "  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  AtlasQuest, v4.4.3\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  Icicle, v0.9\n  LootLog, v1.1\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=104)\n",
			["timestamp"] = "2025-06-23 07:40:43",
			["context"] = "Global",
			["stack"] = "[C]: in function `SetColorTexture'\nInterface\\AddOns\\LootLog\\ItemFrame.lua:51: in function `initialize'\nInterface\\AddOns\\LootLog\\ItemFrame.lua:86: in function `CreateItemFrame'\nInterface\\AddOns\\LootLog\\LootLog.lua:249: in function `init'\nInterface\\AddOns\\LootLog\\LootLog.lua:398: in main chunk\n",
		}, -- [18]
		{
			["message"] = "Interface\\FrameXML\\UIDropDownMenu.lua:861: attempt to index local 'frame' (a nil value)",
			["count"] = 1,
			["addons"] = "  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  AtlasQuest, v4.4.3\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  Icicle, v0.9\n  LootLog, v1.1\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=104)\n",
			["timestamp"] = "2025-06-23 07:40:43",
			["context"] = "Global",
			["stack"] = "[C]: ?\nInterface\\FrameXML\\UIDropDownMenu.lua:861: in function `UIDropDownMenu_SetText'\nInterface\\AddOns\\LootLog\\LootLog.lua:168: in function <Interface\\AddOns\\LootLog\\LootLog.lua:117>\n",
		}, -- [19]
		{
			["message"] = "Interface\\AddOns\\LootLog\\LootLog.lua:54: attempt to call method 'SetItemByID' (a nil value)",
			["count"] = 2,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMPartyClassic, v\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.1\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, vr301\n  SkadaAbsorbs, v1.0\n  SkadaCC, v1.0\n  SkadaDamage, v1.0\n  SkadaDamageTaken, v1.0\n  SkadaDeaths, v1.0\n  SkadaDebuffs, v1.0\n  SkadaDispels, v1.0\n  SkadaEnemies, v1.0\n  SkadaFailbot, v1.0\n  SkadaHealing, v1.0\n  SkadaPower, v1.0\n  SkadaThreat, v1.0\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a6f)\n",
			["timestamp"] = "2025-06-23 08:07:48",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `SetItemByID'\nInterface\\AddOns\\LootLog\\LootLog.lua:54: in function <Interface\\AddOns\\LootLog\\LootLog.lua:35>\nInterface\\AddOns\\LootLog\\LootLog.lua:236: in function <Interface\\AddOns\\LootLog\\LootLog.lua:214>\n",
		}, -- [20]
		{
			["message"] = "Interface\\AddOns\\LootLog\\LootLog.lua:54: attempt to call method 'SetItemByID' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.1\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=518)\n",
			["timestamp"] = "2025-06-23 08:08:43",
			["context"] = "Global",
			["stack"] = "[C]: in function `SetItemByID'\nInterface\\AddOns\\LootLog\\LootLog.lua:54: in function <Interface\\AddOns\\LootLog\\LootLog.lua:35>\nInterface\\AddOns\\LootLog\\LootLog.lua:209: in function <Interface\\AddOns\\LootLog\\LootLog.lua:146>\n",
		}, -- [21]
		{
			["message"] = "Interface\\AddOns\\LootLog\\ItemFrame.lua:116: attempt to call method 'SetItemByID' (a nil value)",
			["count"] = 10,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMPartyClassic, v\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.1\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, vr301\n  SkadaAbsorbs, v1.0\n  SkadaCC, v1.0\n  SkadaDamage, v1.0\n  SkadaDamageTaken, v1.0\n  SkadaDeaths, v1.0\n  SkadaDebuffs, v1.0\n  SkadaDispels, v1.0\n  SkadaEnemies, v1.0\n  SkadaFailbot, v1.0\n  SkadaHealing, v1.0\n  SkadaPower, v1.0\n  SkadaThreat, v1.0\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a6f)\n",
			["timestamp"] = "2025-06-23 08:10:25",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `SetItemByID'\nInterface\\AddOns\\LootLog\\ItemFrame.lua:116: in function <Interface\\AddOns\\LootLog\\ItemFrame.lua:116>\n",
		}, -- [22]
		{
			["message"] = "Interface\\AddOns\\LootLog\\LootLog.lua:37: attempt to index global 'C_DateAndTime' (a nil value)",
			["count"] = 2,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMPartyClassic, v\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.5\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, vr301\n  SkadaAbsorbs, v1.0\n  SkadaCC, v1.0\n  SkadaDamage, v1.0\n  SkadaDamageTaken, v1.0\n  SkadaDeaths, v1.0\n  SkadaDebuffs, v1.0\n  SkadaDispels, v1.0\n  SkadaEnemies, v1.0\n  SkadaFailbot, v1.0\n  SkadaHealing, v1.0\n  SkadaPower, v1.0\n  SkadaThreat, v1.0\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a6f)\n",
			["timestamp"] = "2025-06-23 08:12:55",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\nInterface\\AddOns\\LootLog\\LootLog.lua:37: in function <Interface\\AddOns\\LootLog\\LootLog.lua:33>\nInterface\\AddOns\\LootLog\\LootLog.lua:343: in function `func'\nInterface\\AddOns\\LootLog\\ItemCache.lua:74: in function `event'\nInterface\\AddOns\\LootLog\\ItemCache.lua:40: in function `getAsync'\nInterface\\AddOns\\LootLog\\LootLog.lua:343: in function <Interface\\AddOns\\LootLog\\LootLog.lua:325>\n",
		}, -- [23]
		{
			["message"] = "Interface\\AddOns\\LootLog\\ItemFrame.lua:106: attempt to call method 'SetItemByID' (a nil value)",
			["count"] = 2,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMPartyClassic, v\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.5\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, vr301\n  SkadaAbsorbs, v1.0\n  SkadaCC, v1.0\n  SkadaDamage, v1.0\n  SkadaDamageTaken, v1.0\n  SkadaDeaths, v1.0\n  SkadaDebuffs, v1.0\n  SkadaDispels, v1.0\n  SkadaEnemies, v1.0\n  SkadaFailbot, v1.0\n  SkadaHealing, v1.0\n  SkadaPower, v1.0\n  SkadaThreat, v1.0\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a6f)\n",
			["timestamp"] = "2025-06-23 08:16:04",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `SetItemByID'\nInterface\\AddOns\\LootLog\\ItemFrame.lua:106: in function <Interface\\AddOns\\LootLog\\ItemFrame.lua:105>\n",
		}, -- [24]
		{
			["message"] = "Interface\\AddOns\\LootLog\\LootLog.lua:66: bad argument #1 to 'len' (string expected, got nil)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMPartyClassic, v\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.5\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, vr301\n  SkadaAbsorbs, v1.0\n  SkadaCC, v1.0\n  SkadaDamage, v1.0\n  SkadaDamageTaken, v1.0\n  SkadaDeaths, v1.0\n  SkadaDebuffs, v1.0\n  SkadaDispels, v1.0\n  SkadaEnemies, v1.0\n  SkadaFailbot, v1.0\n  SkadaHealing, v1.0\n  SkadaPower, v1.0\n  SkadaThreat, v1.0\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a6f)\n",
			["timestamp"] = "2025-06-23 08:21:01",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\n[C]: in function `len'\nInterface\\AddOns\\LootLog\\LootLog.lua:66: in function `pad'\nInterface\\AddOns\\LootLog\\LootLog.lua:71: in function <Interface\\AddOns\\LootLog\\LootLog.lua:62>\nInterface\\AddOns\\LootLog\\LootLog.lua:190: in function `?'\nInterface\\AddOns\\LootLog\\LootLog.lua:195: in function <Interface\\AddOns\\LootLog\\LootLog.lua:187>\nInterface\\AddOns\\LootLog\\ItemFrame.lua:109: in function <Interface\\AddOns\\LootLog\\ItemFrame.lua:108>\n",
		}, -- [25]
		{
			["message"] = "Interface\\AddOns\\SkadaDamage\\Damage.lua:272: attempt to index local 'player' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMIcecrown, v\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.5\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  ScrapMerchant, v\n  SexyMap, v\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, vr301\n  SkadaAbsorbs, v1.0\n  SkadaCC, v1.0\n  SkadaDamage, v1.0\n  SkadaDamageTaken, v1.0\n  SkadaDeaths, v1.0\n  SkadaDebuffs, v1.0\n  SkadaDispels, v1.0\n  SkadaEnemies, v1.0\n  SkadaFailbot, v1.0\n  SkadaHealing, v1.0\n  SkadaPower, v1.0\n  SkadaThreat, v1.0\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a7e)\n",
			["timestamp"] = "2025-06-25 20:20:04",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\nInterface\\AddOns\\SkadaDamage\\Damage.lua:272: in function `Enter'\nInterface\\AddOns\\Skada\\Skada.lua:2204: in function `AddSubviewToTooltip'\nInterface\\AddOns\\Skada\\BarDisplay.lua:133: in function <Interface\\AddOns\\Skada\\BarDisplay.lua:113>\nInterface\\AddOns\\Skada\\BarDisplay.lua:225: in function <Interface\\AddOns\\Skada\\BarDisplay.lua:225>\n",
		}, -- [26]
		{
			["message"] = "Error: AddOn TellMeWhen attempted to call a forbidden function (FocusUnit()) from a tainted execution path.",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMIcecrown, v\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.5\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, vr301\n  SkadaAbsorbs, v1.0\n  SkadaCC, v1.0\n  SkadaDamage, v1.0\n  SkadaDamageTaken, v1.0\n  SkadaDeaths, v1.0\n  SkadaDebuffs, v1.0\n  SkadaDispels, v1.0\n  SkadaEnemies, v1.0\n  SkadaFailbot, v1.0\n  SkadaHealing, v1.0\n  SkadaPower, v1.0\n  SkadaThreat, v1.0\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a6b)\n",
			["timestamp"] = "2025-06-25 20:28:35",
			["context"] = "AddOn: TellMeWhen",
			["stack"] = "[C]: in function `FocusUnit'\nInterface\\FrameXML\\UnitPopup.lua:1382: in function `func'\nInterface\\FrameXML\\UIDropDownMenu.lua:583: in function `UIDropDownMenuButton_OnClick'\n[string \"*:OnClick\"]:1: in function <[string \"*:OnClick\"]:1>\n",
		}, -- [27]
		{
			["message"] = "Error: AddOn TellMeWhen attempted to call a forbidden function (ClearFocus()) from a tainted execution path.",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMPartyBC, v\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DBMWorldEvents, v\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.5\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, vr301\n  SkadaAbsorbs, v1.0\n  SkadaCC, v1.0\n  SkadaDamage, v1.0\n  SkadaDamageTaken, v1.0\n  SkadaDeaths, v1.0\n  SkadaDebuffs, v1.0\n  SkadaDispels, v1.0\n  SkadaEnemies, v1.0\n  SkadaFailbot, v1.0\n  SkadaHealing, v1.0\n  SkadaPower, v1.0\n  SkadaThreat, v1.0\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a7e)\n",
			["timestamp"] = "2025-06-26 18:30:00",
			["context"] = "AddOn: TellMeWhen",
			["stack"] = "[C]: in function `ClearFocus'\nInterface\\FrameXML\\UnitPopup.lua:1384: in function `func'\nInterface\\FrameXML\\UIDropDownMenu.lua:583: in function `UIDropDownMenuButton_OnClick'\n[string \"*:OnClick\"]:1: in function <[string \"*:OnClick\"]:1>\n",
		}, -- [28]
		{
			["message"] = "Interface\\AddOns\\Collectinator\\Frame.lua:1576: attempt to call method 'Print' (a nil value)",
			["count"] = 3,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMPartyBC, v\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DBMWorldEvents, v\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.5\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  ScrapMerchant, v\n  SexyMap, v\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, vr301\n  SkadaAbsorbs, v1.0\n  SkadaCC, v1.0\n  SkadaDamage, v1.0\n  SkadaDamageTaken, v1.0\n  SkadaDeaths, v1.0\n  SkadaDebuffs, v1.0\n  SkadaDispels, v1.0\n  SkadaEnemies, v1.0\n  SkadaFailbot, v1.0\n  SkadaHealing, v1.0\n  SkadaPower, v1.0\n  SkadaThreat, v1.0\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a91)\n",
			["timestamp"] = "2025-06-26 18:46:51",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `Print'\nInterface\\AddOns\\Collectinator\\Frame.lua:1576: in function <Interface\\AddOns\\Collectinator\\Frame.lua:1573>\n",
		}, -- [29]
		{
			["message"] = "Interface\\AddOns\\LootLog\\LootLog.lua:227: CreateFrame(): Couldn't find inherited node \"DialogBorderDarkFrameTemplate\"",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=518)\n",
			["timestamp"] = "2025-06-26 19:14:53",
			["context"] = "Global",
			["stack"] = "[C]: ?\n[C]: in function `CreateFrame'\nInterface\\AddOns\\LootLog\\LootLog.lua:227: in main chunk\n",
		}, -- [30]
		{
			["message"] = "Interface\\AddOns\\LootLog\\LootLog.lua:121: attempt to call method 'SetItemByID' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  ScrapMerchant, v\n  SexyMap, v\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, vr301\n  SkadaAbsorbs, v1.0\n  SkadaCC, v1.0\n  SkadaDamage, v1.0\n  SkadaDamageTaken, v1.0\n  SkadaDeaths, v1.0\n  SkadaDebuffs, v1.0\n  SkadaDispels, v1.0\n  SkadaEnemies, v1.0\n  SkadaFailbot, v1.0\n  SkadaHealing, v1.0\n  SkadaPower, v1.0\n  SkadaThreat, v1.0\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a6d)\n",
			["timestamp"] = "2025-06-26 19:18:16",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `SetItemByID'\nInterface\\AddOns\\LootLog\\LootLog.lua:121: in function <Interface\\AddOns\\LootLog\\LootLog.lua:88>\nInterface\\AddOns\\LootLog\\LootLog.lua:355: in function `func'\nInterface\\AddOns\\LootLog\\ItemCache.lua:74: in function `event'\nInterface\\AddOns\\LootLog\\ItemCache.lua:40: in function `getAsync'\nInterface\\AddOns\\LootLog\\LootLog.lua:355: in function <Interface\\AddOns\\LootLog\\LootLog.lua:337>\n",
		}, -- [31]
		{
			["message"] = "Interface\\AddOns\\LootLog\\ItemFrame.lua:106: attempt to call method 'SetItemByID' (a nil value)",
			["count"] = 2,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, vr301\n  SkadaAbsorbs, v1.0\n  SkadaCC, v1.0\n  SkadaDamage, v1.0\n  SkadaDamageTaken, v1.0\n  SkadaDeaths, v1.0\n  SkadaDebuffs, v1.0\n  SkadaDispels, v1.0\n  SkadaEnemies, v1.0\n  SkadaFailbot, v1.0\n  SkadaHealing, v1.0\n  SkadaPower, v1.0\n  SkadaThreat, v1.0\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a5a)\n",
			["timestamp"] = "2025-06-26 19:20:48",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `SetItemByID'\nInterface\\AddOns\\LootLog\\ItemFrame.lua:106: in function <Interface\\AddOns\\LootLog\\ItemFrame.lua:105>\n",
		}, -- [32]
		{
			["message"] = "Interface\\AddOns\\LootLog\\LootLog.lua:6: attempt to call global 'include' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=518)\n",
			["timestamp"] = "2025-06-26 19:41:20",
			["context"] = "Global",
			["stack"] = "[C]: in function `include'\nInterface\\AddOns\\LootLog\\LootLog.lua:6: in main chunk\n",
		}, -- [33]
		{
			["message"] = "Interface\\AddOns\\LootLog\\LootLog.lua:6: attempt to call global 'include' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=518)\n",
			["timestamp"] = "2025-06-26 19:43:10",
			["context"] = "Global",
			["stack"] = "[C]: in function `include'\nInterface\\AddOns\\LootLog\\LootLog.lua:6: in main chunk\n",
		}, -- [34]
		{
			["message"] = "Interface\\AddOns\\LootLog\\LootLog.lua:6: attempt to call global 'dofile' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=518)\n",
			["timestamp"] = "2025-06-26 19:43:40",
			["context"] = "Global",
			["stack"] = "[C]: in function `dofile'\nInterface\\AddOns\\LootLog\\LootLog.lua:6: in main chunk\n",
		}, -- [35]
		{
			["message"] = "Interface\\AddOns\\LootLog\\LootLog.lua:6: attempt to call global 'dofile' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=518)\n",
			["timestamp"] = "2025-06-26 19:45:18",
			["context"] = "Global",
			["stack"] = "[C]: in function `dofile'\nInterface\\AddOns\\LootLog\\LootLog.lua:6: in main chunk\n",
		}, -- [36]
		{
			["message"] = "Interface\\AddOns\\LootLog\\LootLog.lua:6: attempt to call global 'include' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=518)\n",
			["timestamp"] = "2025-06-26 19:46:09",
			["context"] = "Global",
			["stack"] = "[C]: in function `include'\nInterface\\AddOns\\LootLog\\LootLog.lua:6: in main chunk\n",
		}, -- [37]
		{
			["message"] = "Interface\\FrameXML\\UIParent.lua:2567: attempt to compare nil with number",
			["count"] = 1000,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMPartyClassic, v\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  ScrapMerchant, v\n  SexyMap, v\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, vr301\n  SkadaAbsorbs, v1.0\n  SkadaCC, v1.0\n  SkadaDamage, v1.0\n  SkadaDamageTaken, v1.0\n  SkadaDeaths, v1.0\n  SkadaDebuffs, v1.0\n  SkadaDispels, v1.0\n  SkadaEnemies, v1.0\n  SkadaFailbot, v1.0\n  SkadaHealing, v1.0\n  SkadaPower, v1.0\n  SkadaThreat, v1.0\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a82)\n",
			["timestamp"] = "2025-06-26 22:16:57",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\nInterface\\FrameXML\\UIParent.lua:2567: in function <Interface\\FrameXML\\UIParent.lua:2554>\n",
		}, -- [38]
		{
			["message"] = "Interface\\AddOns\\LootLog\\ItemFrame.lua:49: attempt to call method 'SetColorTexture' (a nil value)",
			["count"] = 1,
			["addons"] = "  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  AtlasQuest, v4.4.3\n  Babylonian, v5.1.DEV.130\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=2a6)\n",
			["timestamp"] = "2025-06-28 00:40:39",
			["context"] = "Global",
			["stack"] = "[C]: in function `SetColorTexture'\nInterface\\AddOns\\LootLog\\ItemFrame.lua:49: in function `initialize'\nInterface\\AddOns\\LootLog\\ItemFrame.lua:82: in function `CreateItemFrame'\nInterface\\AddOns\\LootLog\\LootLog.lua:412: in main chunk\n",
		}, -- [39]
		{
			["message"] = "Interface\\AddOns\\LootLog\\ItemFrame.lua:49: attempt to call method 'SetColorTexture' (a nil value)",
			["count"] = 1,
			["addons"] = "  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  AtlasQuest, v4.4.3\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=129)\n",
			["timestamp"] = "2025-06-28 00:41:11",
			["context"] = "Global",
			["stack"] = "[C]: in function `SetColorTexture'\nInterface\\AddOns\\LootLog\\ItemFrame.lua:49: in function `initialize'\nInterface\\AddOns\\LootLog\\ItemFrame.lua:82: in function `CreateItemFrame'\nInterface\\AddOns\\LootLog\\LootLog.lua:412: in main chunk\n",
		}, -- [40]
		{
			["message"] = "Error: AddOn !KRT attempted to call a forbidden function (SetPartyAssignment()) from a tainted execution path.",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, vr301\n  SkadaAbsorbs, v1.0\n  SkadaCC, v1.0\n  SkadaDamage, v1.0\n  SkadaDamageTaken, v1.0\n  SkadaDeaths, v1.0\n  SkadaDebuffs, v1.0\n  SkadaDispels, v1.0\n  SkadaEnemies, v1.0\n  SkadaFailbot, v1.0\n  SkadaHealing, v1.0\n  SkadaPower, v1.0\n  SkadaThreat, v1.0\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a5a)\n",
			["timestamp"] = "2025-07-01 22:24:15",
			["context"] = "AddOn: !KRT",
			["stack"] = "[C]: in function `SetPartyAssignment'\nInterface\\FrameXML\\UnitPopup.lua:1344: in function `func'\nInterface\\FrameXML\\UIDropDownMenu.lua:583: in function `UIDropDownMenuButton_OnClick'\n[string \"*:OnClick\"]:1: in function <[string \"*:OnClick\"]:1>\n",
		}, -- [41]
		{
			["message"] = "Error: AddOn Skada attempted to call a forbidden function (FocusUnit()) from a tainted execution path.",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootWrathoftheLichKing, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMIcecrown, v\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  ScrapMerchant, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, vr301\n  SkadaAbsorbs, v1.0\n  SkadaCC, v1.0\n  SkadaDamage, v1.0\n  SkadaDamageTaken, v1.0\n  SkadaDeaths, v1.0\n  SkadaDebuffs, v1.0\n  SkadaDispels, v1.0\n  SkadaEnemies, v1.0\n  SkadaFailbot, v1.0\n  SkadaHealing, v1.0\n  SkadaPower, v1.0\n  SkadaThreat, v1.0\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=ab6)\n",
			["timestamp"] = "2025-07-02 20:32:49",
			["context"] = "AddOn: Skada",
			["stack"] = "[C]: in function `FocusUnit'\nInterface\\FrameXML\\UnitPopup.lua:1382: in function `func'\nInterface\\FrameXML\\UIDropDownMenu.lua:583: in function `UIDropDownMenuButton_OnClick'\n[string \"*:OnClick\"]:1: in function <[string \"*:OnClick\"]:1>\n",
		}, -- [42]
		{
			["message"] = "Interface\\AddOns\\Recount\\Tracker.lua:3: Cannot find a library instance of \"LibBossIDs-1.0\".",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Recount, v\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=4e1)\n",
			["timestamp"] = "2025-07-02 21:38:12",
			["context"] = "Global",
			["stack"] = "[C]: ?\n[C]: in function `error'\nInterface\\AddOns\\!KRT\\Libs\\LibStub.lua:23: in function `LibStub'\nInterface\\AddOns\\Recount\\Tracker.lua:3: in main chunk\n",
		}, -- [43]
		{
			["message"] = "Interface\\AddOns\\Recount\\GUI_Detail.lua:1: Cannot find a library instance of \"LibGraph-2.0\".",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Recount, v\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=4e1)\n",
			["timestamp"] = "2025-07-02 21:38:12",
			["context"] = "Global",
			["stack"] = "[C]: ?\n[C]: in function `error'\nInterface\\AddOns\\!KRT\\Libs\\LibStub.lua:23: in function `GetLibrary'\nInterface\\AddOns\\Recount\\GUI_Detail.lua:1: in main chunk\n",
		}, -- [44]
		{
			["message"] = "Interface\\AddOns\\Recount\\GUI_DeathGraph.lua:1: Cannot find a library instance of \"LibGraph-2.0\".",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Recount, v\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=4e1)\n",
			["timestamp"] = "2025-07-02 21:38:12",
			["context"] = "Global",
			["stack"] = "[C]: ?\n[C]: in function `error'\nInterface\\AddOns\\!KRT\\Libs\\LibStub.lua:23: in function `GetLibrary'\nInterface\\AddOns\\Recount\\GUI_DeathGraph.lua:1: in main chunk\n",
		}, -- [45]
		{
			["message"] = "Interface\\AddOns\\Recount\\GUI_Graph.lua:1: Cannot find a library instance of \"LibGraph-2.0\".",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Recount, v\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=4e1)\n",
			["timestamp"] = "2025-07-02 21:38:12",
			["context"] = "Global",
			["stack"] = "[C]: ?\n[C]: in function `error'\nInterface\\AddOns\\!KRT\\Libs\\LibStub.lua:23: in function `GetLibrary'\nInterface\\AddOns\\Recount\\GUI_Graph.lua:1: in main chunk\n",
		}, -- [46]
		{
			["message"] = "Interface\\AddOns\\Recount\\GUI_Config.lua:2: Cannot find a library instance of \"LibGraph-2.0\".",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Recount, v\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=4e1)\n",
			["timestamp"] = "2025-07-02 21:38:12",
			["context"] = "Global",
			["stack"] = "[C]: ?\n[C]: in function `error'\nInterface\\AddOns\\!KRT\\Libs\\LibStub.lua:23: in function `GetLibrary'\nInterface\\AddOns\\Recount\\GUI_Config.lua:2: in main chunk\n",
		}, -- [47]
		{
			["message"] = "Interface\\AddOns\\Recount\\GUI_Realtime.lua:1: Cannot find a library instance of \"LibGraph-2.0\".",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Recount, v\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=4e1)\n",
			["timestamp"] = "2025-07-02 21:38:12",
			["context"] = "Global",
			["stack"] = "[C]: ?\n[C]: in function `error'\nInterface\\AddOns\\!KRT\\Libs\\LibStub.lua:23: in function `GetLibrary'\nInterface\\AddOns\\Recount\\GUI_Realtime.lua:1: in main chunk\n",
		}, -- [48]
		{
			["message"] = "Interface\\AddOns\\Recount\\Recount.lua:1938: attempt to call method 'CreateDetailWindow' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Recount, v\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=4e1)\n",
			["timestamp"] = "2025-07-02 21:38:12",
			["context"] = "Global",
			["stack"] = "(tail call): ?\nInterface\\AddOns\\Recount\\Recount.lua:1938: in function <Interface\\AddOns\\Recount\\Recount.lua:1884>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[1]\"]:9: in function <[string \"safecall Dispatcher[1]\"]:5>\n(tail call): ?\n...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:529: in function `InitializeAddon'\n...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:644: in function <...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:636>\n",
		}, -- [49]
		{
			["message"] = "Interface\\AddOns\\Recount\\GUI_Main.lua:679: attempt to index field 'DetailWindow' (a nil value)",
			["count"] = 195,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Recount, v\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=4e1)\n",
			["timestamp"] = "2025-07-02 21:38:12",
			["context"] = "Global",
			["stack"] = "[C]: ?\nInterface\\AddOns\\Recount\\GUI_Main.lua:679: in function `UpdateDetailData'\nInterface\\AddOns\\Recount\\GUI_Main.lua:986: in function `RefreshMainWindow'\nInterface\\AddOns\\Recount\\GUI_Main.lua:675: in function `SetMainWindowMode'\nInterface\\AddOns\\Recount\\GUI_Main.lua:641: in function `LoadMainWindowData'\nInterface\\AddOns\\Recount\\Recount_Modes.lua:633: in function `SetupMainWindow'\nInterface\\AddOns\\Recount\\Recount_Modes.lua:628: in function `AddModeTooltip'\nInterface\\AddOns\\GearScore\\recount.lua:103: in main chunk\n",
		}, -- [50]
		{
			["message"] = "Interface\\AddOns\\Recount\\deletion.lua:22: Usage: UnitIsGhost(\"unit\")",
			["count"] = 3,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  Recount, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=971)\n",
			["timestamp"] = "2025-07-02 21:38:17",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n[C]: ?\nInterface\\AddOns\\Recount\\deletion.lua:22: in function `DetectInstanceChange'\nInterface\\AddOns\\Recount\\Recount.lua:2014: in function <Interface\\AddOns\\Recount\\Recount.lua:1995>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[1]\"]:9: in function <[string \"safecall Dispatcher[1]\"]:5>\n(tail call): ?\n...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:558: in function `EnableAddon'\n...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:651: in function <...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:636>\n[C]: in function `LoadAddOn'\nInterface\\FrameXML\\UIParent.lua:235: in function `UIParentLoadAddOn'\nInterface\\FrameXML\\UIParent.lua:258: in function `CombatLog_LoadUI'\nInterface\\FrameXML\\UIParent.lua:482: in function <Interface\\FrameXML\\UIParent.lua:454>\n",
		}, -- [51]
		{
			["message"] = "Interface\\AddOns\\Recount\\WindowOrder.lua:51: attempt to index field 'Below' (a nil value)",
			["count"] = 5,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMIcecrown, v\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  Recount, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=982)\n",
			["timestamp"] = "2025-07-02 21:39:55",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\nInterface\\AddOns\\Recount\\WindowOrder.lua:51: in function `SetWindowTop'\nInterface\\AddOns\\Recount\\GUI_Main.lua:487: in function <Interface\\AddOns\\Recount\\GUI_Main.lua:479>\n",
		}, -- [52]
		{
			["message"] = "Interface\\AddOns\\Recount\\GUI_Main.lua:582: attempt to call method 'ShowConfig' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMIcecrown, v\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  Recount, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=982)\n",
			["timestamp"] = "2025-07-02 21:40:23",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `ShowConfig'\nInterface\\AddOns\\Recount\\GUI_Main.lua:582: in function <Interface\\AddOns\\Recount\\GUI_Main.lua:582>\n",
		}, -- [53]
		{
			["message"] = "Error: AddOn TellMeWhen attempted to call a forbidden function (FocusUnit()) from a tainted execution path.",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootCrafting, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=994)\n",
			["timestamp"] = "2025-07-05 13:58:13",
			["context"] = "AddOn: TellMeWhen",
			["stack"] = "[C]: in function `FocusUnit'\nInterface\\FrameXML\\UnitPopup.lua:1382: in function `func'\nInterface\\FrameXML\\UIDropDownMenu.lua:583: in function `UIDropDownMenuButton_OnClick'\n[string \"*:OnClick\"]:1: in function <[string \"*:OnClick\"]:1>\n",
		}, -- [54]
		{
			["message"] = "Interface\\AddOns\\Skada\\Modules\\Enemies.lua:476: invalid option in `format'",
			["count"] = 96,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMIcecrown, v\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9ae)\n",
			["timestamp"] = "2025-07-09 20:25:41",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n[C]: ?\nInterface\\AddOns\\Skada\\Modules\\Enemies.lua:476: in function `post_tooltip'\nInterface\\AddOns\\Skada\\Core\\Core.lua:1532: in function `ShowTooltip'\nInterface\\AddOns\\Skada\\Core\\Display\\Bar.lua:236: in function `?'\n...ter\\Libs\\CallbackHandler-1.0\\CallbackHandler-1.0.lua:147: in function <...ter\\Libs\\CallbackHandler-1.0\\CallbackHandler-1.0.lua:147>\n[string \"safecall Dispatcher[3]\"]:4: in function <[string \"safecall Dispatcher[3]\"]:4>\n[C]: ?\n[string \"safecall Dispatcher[3]\"]:13: in function `?'\n...ter\\Libs\\CallbackHandler-1.0\\CallbackHandler-1.0.lua:92: in function `Fire'\n...bs\\SpecializedLibBars-1.0\\SpecializedLibBars-1.0.lua:1740: in function <...bs\\SpecializedLibBars-1.0\\SpecializedLibBars-1.0.lua:1734>\n",
		}, -- [55]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:66: attempt to index global 'C_AddOns' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.0\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=7b3)\n",
			["timestamp"] = "2025-07-10 23:08:20",
			["context"] = "Global",
			["stack"] = "[C]: ?\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:66: in function `CreateConfigMenu'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:19: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:11>\n(tail call): ?\n",
		}, -- [56]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:177: attempt to index global 'Settings' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.0\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=7b3)\n",
			["timestamp"] = "2025-07-10 23:10:08",
			["context"] = "Global",
			["stack"] = "[C]: ?\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:177: in function `CreateConfigMenu'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:19: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:11>\n(tail call): ?\n",
		}, -- [57]
		{
			["message"] = "Interface\\AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:36: unexpected symbol near 'not'",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.0\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=7b3)\n",
			["timestamp"] = "2025-07-10 23:12:04",
			["context"] = "Global",
			["stack"] = "[C]: ?\n",
		}, -- [58]
		{
			["message"] = "...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:18: attempt to index global 'TargetHealthPercentUI' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.0\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=7b3)\n",
			["timestamp"] = "2025-07-10 23:12:04",
			["context"] = "Global",
			["stack"] = "[C]: ?\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:18: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:11>\n(tail call): ?\n",
		}, -- [59]
		{
			["message"] = "...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:36: attempt to index global 'TargetHealthPercentBar' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.0\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9b9)\n",
			["timestamp"] = "2025-07-10 23:12:05",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:36: in function `InitializeBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:25: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:24>\n(tail call): ?\n",
		}, -- [60]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:117: attempt to call method 'SetObeyStepOnDrag' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.0\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=7b3)\n",
			["timestamp"] = "2025-07-10 23:41:19",
			["context"] = "Global",
			["stack"] = "[C]: in function `SetObeyStepOnDrag'\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:117: in function `CreateSlider'\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:65: in function `CreateConfigMenu'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:23: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:14>\n(tail call): ?\n",
		}, -- [61]
		{
			["message"] = "WTF\\Account\\GARIILA\\Icecrown\\Anelny\\SavedVariables\\SkadaStorage.lua:281538: ']' expected near '<eof>'",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=763)\n",
			["timestamp"] = "2025-07-11 01:46:00",
			["context"] = "Global",
			["stack"] = "[C]: ?\n",
		}, -- [62]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:66: attempt to index global 'C_AddOns' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.0\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=7b3)\n",
			["timestamp"] = "2025-07-11 01:52:20",
			["context"] = "Global",
			["stack"] = "[C]: ?\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:66: in function `CreateConfigMenu'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:19: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:11>\n(tail call): ?\n",
		}, -- [63]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:66: attempt to index global 'C_AddOns' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.0\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=7b3)\n",
			["timestamp"] = "2025-07-11 01:52:57",
			["context"] = "Global",
			["stack"] = "[C]: ?\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:66: in function `CreateConfigMenu'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:19: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:11>\n(tail call): ?\n",
		}, -- [64]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:645: attempt to concatenate a nil value",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=7b3)\n",
			["timestamp"] = "2025-07-11 02:12:33",
			["context"] = "Global",
			["stack"] = "[C]: ?\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:645: in function `CreateCheckButton'\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:92: in function `CreateConfigMenu'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:19: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:11>\n(tail call): ?\n",
		}, -- [65]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:645: attempt to concatenate a nil value",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=7b3)\n",
			["timestamp"] = "2025-07-11 02:13:12",
			["context"] = "Global",
			["stack"] = "[C]: ?\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:645: in function `CreateCheckButton'\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:92: in function `CreateConfigMenu'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:19: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:11>\n(tail call): ?\n",
		}, -- [66]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:645: attempt to concatenate a nil value",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=7b3)\n",
			["timestamp"] = "2025-07-11 02:15:29",
			["context"] = "Global",
			["stack"] = "[C]: ?\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:645: in function `CreateCheckButton'\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:92: in function `CreateConfigMenu'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:19: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:11>\n(tail call): ?\n",
		}, -- [67]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:656: attempt to concatenate a nil value",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=7b3)\n",
			["timestamp"] = "2025-07-11 02:20:06",
			["context"] = "Global",
			["stack"] = "[C]: ?\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:656: in function `CreateCheckButton'\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:103: in function `CreateConfigMenu'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:19: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:11>\n(tail call): ?\n",
		}, -- [68]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:126: attempt to concatenate a nil value",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=7b3)\n",
			["timestamp"] = "2025-07-11 02:21:32",
			["context"] = "Global",
			["stack"] = "[C]: ?\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:126: in function `CreateConfigMenu'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:19: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:11>\n(tail call): ?\n",
		}, -- [69]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:617: attempt to index global 'showHPDesc' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=7b3)\n",
			["timestamp"] = "2025-07-11 02:24:26",
			["context"] = "Global",
			["stack"] = "[C]: ?\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:617: in function `CreateConfigMenu'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:19: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:11>\n(tail call): ?\n",
		}, -- [70]
		{
			["message"] = "...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:184: stack overflow",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9b9)\n",
			["timestamp"] = "2025-07-11 03:02:18",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `GetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:184: in function `HandleBarColor'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:125: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:137: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:137: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:137: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:137: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:137: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:137: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:137: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:137: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:137: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:38: in function `InitializeBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:25: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:24>\n(tail call): ?\n",
		}, -- [71]
		{
			["message"] = "...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: stack overflow",
			["count"] = 6,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9f3)\n",
			["timestamp"] = "2025-07-11 03:02:40",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:134: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:134: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:134: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:134: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:134: in function `ShowOrHideBar'\n...\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:134: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:134: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:342: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:340>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[2]\"]:9: in function <[string \"safecall Dispatcher[2]\"]:5>\n(tail call): ?\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:818: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:639>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[3]\"]:9: in function <[string \"safecall Dispatcher[3]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...er\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-CheckBox.lua:68: in function <...er\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-CheckBox.lua:57>\n",
		}, -- [72]
		{
			["message"] = "WTF\\Account\\GARIILA\\Icecrown\\Anelny\\SavedVariables\\Spy.lua:11120: unexpected symbol near '<eof>'",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=797)\n",
			["timestamp"] = "2025-07-11 03:07:34",
			["context"] = "Global",
			["stack"] = "[C]: ?\n",
		}, -- [73]
		{
			["message"] = "...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:189: attempt to index global 'options' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=7b3)\n",
			["timestamp"] = "2025-07-11 03:16:52",
			["context"] = "Global",
			["stack"] = "[C]: ?\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:189: in main chunk\n",
		}, -- [74]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:50: attempt to call field 'GetConfigValue' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=7b3)\n",
			["timestamp"] = "2025-07-11 03:16:52",
			["context"] = "Global",
			["stack"] = "[C]: in function `GetConfigValue'\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:50: in function `CreatethpBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:18: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:11>\n(tail call): ?\n",
		}, -- [75]
		{
			["message"] = "...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:36: attempt to call field 'GetConfigValue' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9b9)\n",
			["timestamp"] = "2025-07-11 03:16:53",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `GetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:36: in function `InitializeBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:25: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:24>\n(tail call): ?\n",
		}, -- [76]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:95: Usage: <unnamed>:SetAlpha(alpha)",
			["count"] = 1000,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9f3)\n",
			["timestamp"] = "2025-07-12 05:28:23",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n[C]: in function `SetAlpha'\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:95: in function `HandleBarColor'\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:88: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:66: in function `UpdateBar'\n...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:43: in function <...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:31>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[3]\"]:9: in function <[string \"safecall Dispatcher[3]\"]:5>\n(tail call): ?\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:818: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:639>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[4]\"]:9: in function <[string \"safecall Dispatcher[4]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...mpat\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-Button.lua:22: in function <...mpat\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-Button.lua:19>\n",
		}, -- [77]
		{
			["message"] = "...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:229: attempt to index local 'c' (a nil value)",
			["count"] = 1000,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9f3)\n",
			["timestamp"] = "2025-07-12 05:28:23",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:229: in function `member'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:249: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:197>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1352: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1075>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1616: in function `FeedGroup'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1896: in function `Open'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:848: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:639>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[4]\"]:9: in function <[string \"safecall Dispatcher[4]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...mpat\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-Button.lua:22: in function <...mpat\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-Button.lua:19>\n",
		}, -- [78]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:105: Usage: <unnamed>:SetAlpha(alpha)",
			["count"] = 1000,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9f3)\n",
			["timestamp"] = "2025-07-12 05:30:57",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n[C]: in function `SetAlpha'\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:105: in function `HandleBarColor'\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:97: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:66: in function `UpdateBar'\n...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:43: in function <...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:31>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[3]\"]:9: in function <[string \"safecall Dispatcher[3]\"]:5>\n(tail call): ?\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:818: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:639>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[4]\"]:9: in function <[string \"safecall Dispatcher[4]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...mpat\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-Button.lua:22: in function <...mpat\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-Button.lua:19>\n",
		}, -- [79]
		{
			["message"] = "...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:229: attempt to index local 'c' (a nil value)",
			["count"] = 529,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9f3)\n",
			["timestamp"] = "2025-07-12 05:30:57",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:229: in function `member'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:249: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:197>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1352: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1075>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1616: in function `FeedGroup'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1896: in function `Open'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:848: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:639>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[4]\"]:9: in function <[string \"safecall Dispatcher[4]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...mpat\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-Button.lua:22: in function <...mpat\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-Button.lua:19>\n",
		}, -- [80]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:27: attempt to index field 'barPos' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9f3)\n",
			["timestamp"] = "2025-07-12 05:31:24",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:27: in function <...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:22>\n",
		}, -- [81]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:105: Usage: <unnamed>:SetAlpha(alpha)",
			["count"] = 359,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9f3)\n",
			["timestamp"] = "2025-07-12 05:32:40",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n[C]: in function `SetAlpha'\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:105: in function `HandleBarColor'\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:97: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:66: in function `UpdateBar'\n...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:43: in function <...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:31>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[3]\"]:9: in function <[string \"safecall Dispatcher[3]\"]:5>\n(tail call): ?\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:818: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:639>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[4]\"]:9: in function <[string \"safecall Dispatcher[4]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...mpat\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-Button.lua:22: in function <...mpat\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-Button.lua:19>\n",
		}, -- [82]
		{
			["message"] = "...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:229: attempt to index local 'c' (a nil value)",
			["count"] = 313,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9f3)\n",
			["timestamp"] = "2025-07-12 05:32:40",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:229: in function `member'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:249: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:197>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1352: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1075>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1616: in function `FeedGroup'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1896: in function `Open'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:848: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:639>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[4]\"]:9: in function <[string \"safecall Dispatcher[4]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...mpat\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-Button.lua:22: in function <...mpat\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-Button.lua:19>\n",
		}, -- [83]
		{
			["message"] = "...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:262: attempt to index field 'db' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9b9)\n",
			["timestamp"] = "2025-07-12 18:14:04",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:262: in function `GetConfigValue'\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:35: in function `CreatethpBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:51: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:44>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[1]\"]:9: in function <[string \"safecall Dispatcher[1]\"]:5>\n(tail call): ?\n...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:558: in function `EnableAddon'\n...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:651: in function <...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:636>\n[C]: in function `LoadAddOn'\nInterface\\FrameXML\\UIParent.lua:235: in function `UIParentLoadAddOn'\nInterface\\FrameXML\\UIParent.lua:258: in function `CombatLog_LoadUI'\nInterface\\FrameXML\\UIParent.lua:482: in function <Interface\\FrameXML\\UIParent.lua:454>\n",
		}, -- [84]
		{
			["message"] = "...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:266: attempt to index field 'db' (a nil value)",
			["count"] = 1000,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9b9)\n",
			["timestamp"] = "2025-07-12 18:19:59",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:266: in function `GetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:375: in function `HandleBarColor'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:155: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:84: in function `OnUpdate'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:73: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:72>\n",
		}, -- [85]
		{
			["message"] = "WTF\\Account\\GARIILA\\Icecrown\\Anelny\\SavedVariables\\SkadaStorage.lua:83873: unexpected symbol near '<eof>'",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=763)\n",
			["timestamp"] = "2025-07-12 19:02:40",
			["context"] = "Global",
			["stack"] = "[C]: ?\n",
		}, -- [86]
		{
			["message"] = "...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:351: attempt to index field 'customDB' (a nil value)",
			["count"] = 1000,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.2\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9b9)\n",
			["timestamp"] = "2025-07-12 19:02:45",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:351: in function `GetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:472: in function `HandleBarColor'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:226: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:155: in function `OnUpdate'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:144: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:143>\n",
		}, -- [87]
		{
			["message"] = "...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:351: attempt to index field 'customDB' (a nil value)",
			["count"] = 1000,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.2\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9b9)\n",
			["timestamp"] = "2025-07-12 19:03:35",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:351: in function `GetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:472: in function `HandleBarColor'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:226: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:155: in function `OnUpdate'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:144: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:143>\n",
		}, -- [88]
		{
			["message"] = "...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:462: Usage: TargetHealthPercentBarBackground:SetAlpha(alpha)",
			["count"] = 1000,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.2\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9f3)\n",
			["timestamp"] = "2025-07-12 21:01:01",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\n[C]: in function `SetAlpha'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:462: in function `HandleBarColor'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:171: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:96: in function `OnUpdate'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:84: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:82>\n",
		}, -- [89]
		{
			["message"] = "...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:178: attempt to index upvalue 'L' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.2\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9b9)\n",
			["timestamp"] = "2025-07-12 22:31:49",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\n...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:178: in function `recreateOptions'\n...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:305: in function `InitializeConfig'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:62: in function `Callback'\nInterface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:16: in function <Interface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:15>\n",
		}, -- [90]
		{
			["message"] = "Interface\\AddOns\\LootLog\\LootLog.lua:110: '=' expected near 'continue'",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=518)\n",
			["timestamp"] = "2025-07-13 00:25:05",
			["context"] = "Global",
			["stack"] = "[C]: ?\n",
		}, -- [91]
		{
			["message"] = "Interface\\AddOns\\LootLog\\LootLog.lua:110: '=' expected near 'continue'",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=518)\n",
			["timestamp"] = "2025-07-13 00:27:02",
			["context"] = "Global",
			["stack"] = "[C]: ?\n",
		}, -- [92]
		{
			["message"] = "Interface\\AddOns\\LootLog\\LootLog.lua:115: '=' expected near 'continue'",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=518)\n",
			["timestamp"] = "2025-07-13 00:29:02",
			["context"] = "Global",
			["stack"] = "[C]: ?\n",
		}, -- [93]
		{
			["message"] = "...Ons\\MouseSpeedEnhanced\\MouseSpeedEnhanced_Config.lua:126: attempt to index global 'MouseSpeedEnhanced' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeedEnhanced, v2.0\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=57d)\n",
			["timestamp"] = "2025-07-13 16:40:37",
			["context"] = "Global",
			["stack"] = "[C]: ?\n...Ons\\MouseSpeedEnhanced\\MouseSpeedEnhanced_Config.lua:126: in main chunk\n",
		}, -- [94]
		{
			["message"] = "...Ons\\MouseSpeedEnhanced\\MouseSpeedEnhanced_Config.lua:126: attempt to index global 'MouseSpeedEnhanced' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeedEnhanced, v2.0.0\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=57f)\n",
			["timestamp"] = "2025-07-13 16:41:26",
			["context"] = "Global",
			["stack"] = "[C]: ?\n...Ons\\MouseSpeedEnhanced\\MouseSpeedEnhanced_Config.lua:126: in main chunk\n",
		}, -- [95]
		{
			["message"] = "Interface\\AddOns\\Skada\\Modules\\Enemies.lua:476: invalid option in `format'",
			["count"] = 32,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMChamberOfAspects, v\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  ScrapMerchant, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a31)\n",
			["timestamp"] = "2025-07-13 22:31:49",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n[C]: ?\nInterface\\AddOns\\Skada\\Modules\\Enemies.lua:476: in function `post_tooltip'\nInterface\\AddOns\\Skada\\Core\\Core.lua:1532: in function `ShowTooltip'\nInterface\\AddOns\\Skada\\Core\\Display\\Bar.lua:236: in function `?'\n...ter\\Libs\\CallbackHandler-1.0\\CallbackHandler-1.0.lua:147: in function <...ter\\Libs\\CallbackHandler-1.0\\CallbackHandler-1.0.lua:147>\n[string \"safecall Dispatcher[3]\"]:4: in function <[string \"safecall Dispatcher[3]\"]:4>\n[C]: ?\n[string \"safecall Dispatcher[3]\"]:13: in function `?'\n...ter\\Libs\\CallbackHandler-1.0\\CallbackHandler-1.0.lua:92: in function `Fire'\n...bs\\SpecializedLibBars-1.0\\SpecializedLibBars-1.0.lua:1740: in function <...bs\\SpecializedLibBars-1.0\\SpecializedLibBars-1.0.lua:1734>\n",
		}, -- [96]
		{
			["message"] = "[string \"SetCameraDistance(30)\"]:1: attempt to call global 'SetCameraDistance' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-13 23:13:43",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `SetCameraDistance'\n[string \"SetCameraDistance(30)\"]:1: in main chunk\n[C]: in function `RunScript'\nInterface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:393: in function `?'\n...ddOns\\Chatter\\Libs\\AceConsole-3.0\\AceConsole-3.0.lua:94: in function `value'\nInterface\\FrameXML\\ChatFrame.lua:4070: in function `ChatEdit_ParseText'\nInterface\\FrameXML\\ChatFrame.lua:3660: in function `ChatEdit_SendText'\nInterface\\FrameXML\\ChatFrame.lua:3698: in function `ChatEdit_OnEnterPressed'\n[string \"*:OnEnterPressed\"]:1: in function <[string \"*:OnEnterPressed\"]:1>\n",
		}, -- [97]
		{
			["message"] = "[string \"SetCameraDistance(10)\"]:1: attempt to call global 'SetCameraDistance' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-13 23:14:47",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `SetCameraDistance'\n[string \"SetCameraDistance(10)\"]:1: in main chunk\n[C]: in function `RunScript'\nInterface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:393: in function `?'\n...ddOns\\Chatter\\Libs\\AceConsole-3.0\\AceConsole-3.0.lua:94: in function `?'\nInterface\\FrameXML\\ChatFrame.lua:4049: in function `ChatEdit_ParseText'\nInterface\\FrameXML\\ChatFrame.lua:3660: in function `ChatEdit_SendText'\nInterface\\FrameXML\\ChatFrame.lua:3698: in function `ChatEdit_OnEnterPressed'\n[string \"*:OnEnterPressed\"]:1: in function <[string \"*:OnEnterPressed\"]:1>\n",
		}, -- [98]
		{
			["message"] = "[string \"SetCameraDistance(20)\"]:1: attempt to call global 'SetCameraDistance' (a nil value)",
			["count"] = 2,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-13 23:14:55",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `SetCameraDistance'\n[string \"SetCameraDistance(20)\"]:1: in main chunk\n[C]: in function `RunScript'\nInterface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:393: in function `?'\n...ddOns\\Chatter\\Libs\\AceConsole-3.0\\AceConsole-3.0.lua:94: in function `?'\nInterface\\FrameXML\\ChatFrame.lua:4049: in function `ChatEdit_ParseText'\nInterface\\FrameXML\\ChatFrame.lua:3660: in function `ChatEdit_SendText'\nInterface\\FrameXML\\ChatFrame.lua:3698: in function `ChatEdit_OnEnterPressed'\n[string \"*:OnEnterPressed\"]:1: in function <[string \"*:OnEnterPressed\"]:1>\n",
		}, -- [99]
		{
			["message"] = "[string \"SetCameraDistance(15)\"]:1: attempt to call global 'SetCameraDistance' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-13 23:15:04",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `SetCameraDistance'\n[string \"SetCameraDistance(15)\"]:1: in main chunk\n[C]: in function `RunScript'\nInterface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:393: in function `?'\n...ddOns\\Chatter\\Libs\\AceConsole-3.0\\AceConsole-3.0.lua:94: in function `?'\nInterface\\FrameXML\\ChatFrame.lua:4049: in function `ChatEdit_ParseText'\nInterface\\FrameXML\\ChatFrame.lua:3660: in function `ChatEdit_SendText'\nInterface\\FrameXML\\ChatFrame.lua:3698: in function `ChatEdit_OnEnterPressed'\n[string \"*:OnEnterPressed\"]:1: in function <[string \"*:OnEnterPressed\"]:1>\n",
		}, -- [100]
		{
			["message"] = "Interface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:115: attempt to call global 'GetCameraZoom' (a nil value)",
			["count"] = 2,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-14 00:45:17",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\nInterface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:115: in function `AutoDetectOptimalDistance'\nInterface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:166: in function `ApplyCameraSettings'\nInterface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:207: in function `ApplySettings'\n...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:93: in function <...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:89>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[2]\"]:9: in function <[string \"safecall Dispatcher[2]\"]:5>\n(tail call): ?\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:818: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:639>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[3]\"]:9: in function <[string \"safecall Dispatcher[3]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...er\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-CheckBox.lua:68: in function <...er\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-CheckBox.lua:57>\n",
		}, -- [101]
	},
}
