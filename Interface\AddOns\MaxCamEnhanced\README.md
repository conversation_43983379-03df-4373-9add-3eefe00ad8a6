# MaxCamEnhanced

Advanced camera distance control addon for World of Warcraft 3.3.5a with configurable settings and user interface.

## Features

- **Camera Unification**: Set the same camera distance for all characters (recommended)
- **Auto-Detection**: Automatically detects optimal camera distance for each character
- **Configurable Camera Distance**: Set maximum camera distance from 1 to 100 units
- **User Interface**: Full configuration interface using Ace3 framework
- **Auto-Apply Settings**: Automatically apply changes when you modify them
- **Zone Change Support**: Optionally reapply settings when entering new zones
- **Toggle On/Off**: Enable or disable camera distance extension
- **Slash Commands**: Quick access via chat commands
- **Localization**: Supports English and Russian languages
- **Saved Settings**: All settings are saved per character

## Installation

1. Extract the MaxCamEnhanced folder to your `Interface/AddOns/` directory
2. Restart World of Warcraft or reload UI (`/reload`)
3. The addon will automatically load and apply default settings

## Usage

### Slash Commands

- `/maxcam` or `/mc` - Open settings interface
- `/maxcam config` - Open settings interface
- `/maxcam apply` - Apply current settings immediately
- `/maxcam toggle` - Toggle camera distance on/off
- `/maxcam set <number>` - Set distance directly (1-100), e.g. `/maxcam set 10`
- `/maxcam info` - Show character-specific camera information
- `/maxcam reset` - Reset all settings to defaults
- `/maxcam help` - Show available commands

### Settings Interface

Access the settings through:
- Slash command: `/maxcam`
- Game Menu: ESC → Interface → AddOns → MaxCamEnhanced

### Settings Options

- **Enable Camera Distance**: Toggle the camera distance extension on/off
- **Auto Apply Changes**: Automatically apply settings when you change them
- **Unify Camera Distance**: Set the same camera distance for all characters (recommended)
- **Auto-Detect Optimal Distance**: Automatically detect best distance for each character
- **Maximum Camera Distance**: Set distance from 1 to 100 units (default WoW is 15)
- **Apply on Zone Change**: Reapply settings when entering new zones
- **Current Camera Values**: Real-time display of current WTF file values

## Default Settings

- Camera Distance: 30 units
- Enabled: Yes
- Auto Apply: Yes
- Apply on Zone Change: Yes
- Unify Camera Distance: Yes (recommended)
- Unified Distance: 25 units
- Auto-Detect Distance: No (disabled when unify is enabled)

## Camera Unification Feature (Recommended)

The **Camera Unification** feature solves the problem of different characters needing different camera settings by setting the same camera distance for all characters automatically.

### How Camera Unification Works (Simplified Architecture):
1. **Uses SetCVar**: Simple `SetCVar()` calls like MouseSpeedEnhanced
2. **Sets Maximum Distance**: `SetCVar("cameraDistanceMax", distance)`
3. **Unifies Saved Distance**: `SetCVar("cameraSavedDistance", unified_distance)`
4. **Standardizes Camera Pitch**: `SetCVar("cameraSavedPitch", "20")`
5. **Silent Operation**: No spam messages, works quietly in background

### Benefits:
- **No Manual Configuration**: Works the same on every character
- **Consistent Experience**: Same camera behavior everywhere
- **No Character-Specific Issues**: Eliminates the need for different settings
- **Simple Setup**: Enable once, works for all characters

### Using Camera Unification:
Simply enable "Unify Camera Distance" in the addon settings. The addon will automatically:
- Set `cameraDistanceMax` to 30 (configurable)
- Set `cameraSavedDistance` to 25 (configurable)
- Set `cameraSavedPitch` to 20 (standard angle)

**No commands needed** - it works automatically when you log in to any character!

You can set any distance from 1 to 100 units to match your preference.

### Technical Implementation:
This addon uses a hybrid approach for maximum reliability:
- **Settings Storage**: SavedVariables for addon configuration
- **Game Application**: `SetCVar()` calls for immediate effect
- **Forced Persistence**: `ConsoleExec()` and `cvar_save` to force WTF file updates
- **Guaranteed Unification**: Overrides any existing WTF values on every login

### Automatic Unification:
When unification is enabled, the addon will:
1. **Override existing WTF values** regardless of what's currently saved
2. **Force immediate save** to config-cache.wtf files
3. **Ensure consistency** across all characters and sessions
4. **Work automatically** on every login, no manual intervention needed

### Real-Time Monitoring:
The configuration interface shows:
- **Current WTF Values**: Live display of cameraSavedDistance, cameraDistanceMax, cameraSavedPitch
- **Unification Target**: Shows what values will be applied when unification is enabled
- **Status Information**: Indicates whether unification is active or not
- **No Manual Actions Needed**: Everything happens automatically

### Configuration:
- **Unified Camera Distance**: The distance that will be set for all characters (1-100, default: 25)
- **Maximum Camera Distance**: The maximum allowed distance (1-100, default: 30)

## WoW 3.3.5a Compatibility

### Supported Features:
- ✅ **Camera Distance Control**: Fully supported
- ✅ **Auto-Detection**: Works using `cameraSavedDistance` and `cameraDistanceMax`
- ✅ **Unification**: Fully supported
- ⚠️ **Camera Pitch Control**: May not work reliably in WoW 3.3.5a

### Known Limitations:
- `GetCameraZoom()` function doesn't exist in 3.3.5a
- Camera pitch control may be disabled automatically if not supported
- Auto-detection uses saved camera distance instead of real-time camera position

## 📋 **Configuration Interface with Tabs**

MaxCamEnhanced features a modern tabbed interface for easy configuration:

### 🎛️ **General Tab**
- **Basic Settings**: Enable/disable, auto-apply, unification
- **Camera Distance**: Manual distance control (1-100)
- **Camera Pitch**: Angle control (if supported)
- **Actions**: Apply settings, reset to defaults

### 🤖 **Auto-Detection Tab**
- **Location-Based Distances**: Configure automatic distances by location type
- **Zone Overrides**: Specific settings for individual zones
- **Real-time Configuration**: Changes apply immediately when auto-detection is enabled

## Location-Based Auto-Detection Feature

MaxCamEnhanced can automatically adjust camera distance based on your current location and environment:

### 🎯 **Configurable Location Types:**
- **🏰 Instances (Dungeons/Raids)**: Default 25, configurable 1-100
- **🏠 Indoor Locations**: Default 20, configurable 1-100
- **🌍 Outdoor Areas**: Default 40, configurable 1-100
- **⚔️ PvP Zones**: Default 50, configurable 1-100
- **🏙️ Cities**: Default 28, configurable 1-100

### 🗺️ **Pre-configured Zone Overrides:**
- **Large Raids**: Naxxramas (30), Icecrown Citadel (30)
- **Small Dungeons**: Deadmines (20), Ragefire Chasm (20), Wailing Caverns (20)
- **PvP Areas**: Wintergrasp (50), Alterac Valley (50), Arathi Basin (45), Warsong Gulch (45)
- **Starting Zones**: Elwynn Forest (35), Dun Morogh (35), Teldrassil (35), Durotar (35), Mulgore (35), Tirisfal Glades (35)
- **High-Level Content**: Northrend (45), Outland (45), Icecrown (45), Storm Peaks (45), Dragonblight (45)

### How Auto-Detection Works:
1. **Analyzes Current Settings**: Reads your character's `cameraSavedDistance` from WoW
2. **Calculates Optimal Buffer**: Adds appropriate buffer based on your preferences:
   - **≤15 units**: Sets limit to 25 (moderate buffer for close camera users)
   - **≤20 units**: Sets limit to 30 (good buffer for standard users)
   - **≤25 units**: Sets limit to 35 (larger buffer for far camera users)
   - **>25 units**: Sets limit to current + 15 (maximum buffer for very far users)

### Using Auto-Detection:
```bash
# Automatic detection on login (if enabled in settings)
# OR manual detection:
/maxcam auto

# Check what it detected:
/maxcam info
```

### Benefits:
- **No Manual Configuration**: Each character gets optimal settings automatically
- **Preserves Preferences**: Respects your current camera distance choices
- **Intelligent Buffering**: Provides appropriate headroom for each playstyle
- **One-Time Setup**: Run once per character, settings are saved

## Why Different Characters Need Different Values

You've noticed that different characters on the same server require different camera distance values (e.g., anelny needs 10, kyklyxklan needs 20). This is a known WoW quirk with several possible causes:

### Possible Causes:
1. **Character-specific CVar Storage**: Each character stores camera settings independently in WTF folder
2. **Race/Model Differences**: Different character races may have slightly different camera collision detection
3. **Character Height**: Taller/shorter characters might affect camera positioning calculations
4. **Login Order**: The order you log into characters can affect which settings "stick"
5. **WTF File Corruption**: Individual character config files may have different base values
6. **Add-on Load Order**: Different characters might load addons in different sequences
7. **Previous Manual Changes**: Characters may have had manual `/console` commands applied before

### How MaxCamEnhanced Handles This:
- **Wide Range**: Supports values from 1 to 100 to accommodate all character quirks
- **Per-Character Settings**: Each character saves their own optimal distance automatically
- **Easy Testing**: Use `/maxcam set <number>` to quickly test different values
- **Instant Feedback**: See results immediately without reloading UI
- **Character-Specific Storage**: Settings are saved per character, not account-wide

### Setting Up Camera Distance:

#### Option 1: Camera Unification (Recommended)
Enable "Unify Camera Distance" in addon settings - works automatically for all characters!

#### Option 2: Auto-Detection per Character
Enable "Auto-Detect Optimal Distance" in settings - detects optimal distance per character automatically.

#### Option 3: Manual Configuration
1. **Log into each character separately** and test individually
2. Start with the default value (20): `/maxcam set 20`
3. If camera doesn't zoom out enough, try lower values: `/maxcam set 10` or `/maxcam set 5`
4. If you want more distance, try higher values: `/maxcam set 30` or `/maxcam set 50`
5. **Test immediately** - the camera distance changes right away
6. **Each character remembers** their own optimal setting automatically

### Quick Setup Examples:
```bash
# Recommended - Enable unification in settings, then it works automatically!

# Manual testing if needed:
/maxcam set 30    # Set specific distance manually
/maxcam info      # See current settings and recommendations
/maxcam toggle    # Turn off to compare with default WoW distance
```

### Debugging Character Differences:
Use `/maxcam info` on each character to see:
- Character name and race
- Current camera distance setting
- Whether the addon is enabled
- Current WoW CVar value
- Default WoW distance for comparison

This helps identify patterns - maybe certain races need different values, or some characters have corrupted settings.

### Possible Solutions to Try:
1. **Check WTF folder**: Look in `WTF/Account/[AccountName]/[ServerName]/[CharacterName]/config-cache.wtf` for different `cameraDistanceMax` values
2. **Reset character settings**: Delete the character's config-cache.wtf file and restart WoW
3. **Test race patterns**: See if characters of the same race need similar values
4. **Check addon conflicts**: Temporarily disable other camera addons and test
5. **Manual CVar reset**: Use `/console cameraDistanceMax 15` to reset to WoW default, then test addon

## Comparison with Original MaxCam

### Original MaxCam
- Fixed distance of 20 units
- No user interface
- No configuration options
- Applied on every zone change
- No way to disable without removing addon

### MaxCamEnhanced
- ✅ **Camera unification for consistent experience across all characters**
- ✅ Auto-detection of optimal distance per character
- ✅ Configurable distance (1-100 units)
- ✅ Full user interface with Ace3
- ✅ Comprehensive settings options
- ✅ Can be toggled on/off
- ✅ Optional zone change application
- ✅ Slash commands for quick access
- ✅ Localization support (EN/RU)
- ✅ Saved settings per character

## Technical Details

- Built with Ace3 framework for professional addon standards
- Uses AceDB for settings persistence
- Event-driven architecture for optimal performance
- Follows WoW addon best practices
- Compatible with World of Warcraft 3.3.5a

## Version History

### Version 2.0
- Complete rewrite with Ace3 framework
- Added configuration interface
- Added localization support
- Added toggle functionality
- Added configurable distance range
- Added auto-apply and zone change options

### Version 1.0 (Original)
- Basic camera distance extension
- Fixed 20 unit distance
- No configuration options
