-- MouseSpeedEnhanced Test Functions
-- Use /testmouse to run tests

local function TestMouseSettings()
    print("=== Testing Mouse Settings ===")
    
    -- Test current mouse settings
    local currentMouseSpeed = GetCVar("mouseSpeed") or "Unknown"
    local currentCameraYaw = GetCVar("cameraYawMoveSpeed") or "Unknown"
    local currentCameraPitch = GetCVar("cameraPitchMoveSpeed") or "Unknown"
    
    print("Current WTF Values:")
    print("• Mouse Speed:", currentMouseSpeed)
    print("• Camera Yaw Speed:", currentCameraYaw)
    print("• Camera Pitch Speed:", currentCameraPitch)
    
    -- Test addon settings
    if MouseSpeedEnhanced and MouseSpeedEnhanced.db and MouseSpeedEnhanced.db.profile then
        local profile = MouseSpeedEnhanced.db.profile
        print("\nAddon Settings:")
        print("• Mouse Speed:", profile.mouseSpeed)
        print("• Camera Yaw Speed:", profile.cameraYawMoveSpeed)
        print("• Camera Pitch Speed:", profile.cameraPitchMoveSpeed)
        print("• Auto Apply:", profile.autoApply and "YES" or "NO")
        print("• Enable on Login:", profile.enableOnLogin and "YES" or "NO")
        
        -- Test if values match
        local mouseMatch = tostring(profile.mouseSpeed) == currentMouseSpeed
        local yawMatch = tostring(profile.cameraYawMoveSpeed) == currentCameraYaw
        local pitchMatch = tostring(profile.cameraPitchMoveSpeed) == currentCameraPitch
        
        print("\nValue Comparison:")
        print("• Mouse Speed Match:", mouseMatch and "YES" or "NO")
        print("• Camera Yaw Match:", yawMatch and "YES" or "NO")
        print("• Camera Pitch Match:", pitchMatch and "YES" or "NO")
        
        if mouseMatch and (not profile.enableOnLogin or (yawMatch and pitchMatch)) then
            print("✅ Settings are correctly applied!")
        else
            print("⚠️ Settings mismatch detected")
        end
    else
        print("MouseSpeedEnhanced not loaded or database not found")
    end
end

local function TestConfigInterface()
    print("\n=== Testing Configuration Interface ===")
    
    if MouseSpeedEnhanced and MouseSpeedEnhanced.InitializeConfig then
        print("Initializing configuration...")
        MouseSpeedEnhanced.InitializeConfig()
        print("Configuration initialized successfully!")
        
        -- Test opening interface
        print("Opening interface...")
        InterfaceOptionsFrame_OpenToCategory("MouseSpeedEnhanced")
        print("Interface should be open now")
        
        -- Test current values display
        print("\n=== Testing Current Values Display ===")
        if MouseSpeedEnhanced.db and MouseSpeedEnhanced.db.profile then
            print("Database found - current values should display correctly")
            print("Check the 'Current WTF Values' section in the interface")
        else
            print("Database not found - current values may not display")
        end
    else
        print("MouseSpeedEnhanced not loaded or InitializeConfig not found")
    end
end

local function TestApplySettings()
    print("\n=== Testing Apply Settings ===")
    
    if MouseSpeedEnhanced and MouseSpeedEnhanced.ApplySettings then
        print("Applying current settings...")
        MouseSpeedEnhanced:ApplySettings()
        print("Settings applied!")
        
        -- Check if UpdateCurrentValues exists
        if MouseSpeedEnhanced.UpdateCurrentValues then
            print("Updating current values display...")
            MouseSpeedEnhanced:UpdateCurrentValues()
            print("Display updated!")
        else
            print("UpdateCurrentValues function not found")
        end
    else
        print("MouseSpeedEnhanced not loaded or ApplySettings not found")
    end
end

local function TestMouseFunctions()
    print("=== Testing Mouse Functions in WoW 3.3.5a ===")
    
    -- Test available mouse functions
    local functions = {
        "GetCVar",
        "SetCVar",
        "ConsoleExec"
    }
    
    for _, funcName in ipairs(functions) do
        local func = _G[funcName]
        if func then
            print(funcName .. ": Available")
        else
            print(funcName .. ": NOT AVAILABLE")
        end
    end
    
    TestMouseSettings()
    TestConfigInterface()
    TestApplySettings()
end

-- Create slash command for testing
SLASH_TESTMOUSE1 = "/testmouse"
SlashCmdList["TESTMOUSE"] = function(msg)
    TestMouseFunctions()
end

print("MouseSpeedEnhanced Test Functions loaded. Use /testmouse to run tests.")
